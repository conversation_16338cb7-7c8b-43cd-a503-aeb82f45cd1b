[project]
# Support Python 3.10+.
requires-python = ">=3.10"

[tool.ruff]
# Set the maximum line length to 120.
line-length = 120

[tool.ruff.lint.isort]
line-length = 120

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
indent-width = 4
docstring-code-format = true
docstring-code-line-length = 120

# Enable auto-formatting of code examples in docstrings. Markdown,
# reStructuredText code/literal blocks and doctests are all supported.
#
# This is currently disabled by default, but it is planned for this
# to be opt-out in the future.
docstring-code-format = false

# Set the line length limit used when formatting code snippets in
# docstrings.
#
# This only has an effect when the `docstring-code-format` setting is
# enabled.
docstring-code-line-length = "dynamic"
exclude = ["*.pyi"]

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint]
select = [
    # pep8-naming
    "N",
    # pycodestyle
    "E",
    # Pyflakes
    "F",
    # pyupgrade
    "UP",
    # flake8-bugbear
    "B",
    # flake8-simplify
    "SIM",
    # isort
    "I",
]