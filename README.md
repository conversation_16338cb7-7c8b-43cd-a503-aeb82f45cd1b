# Odometry Evaluation Code

## Getting Started

### Evaluation for GPS data
Follow the bellow command to evaluate your trajectory with a GT gps data in format `.srt`

```python odom_eval_gps.py -gt PATH_TO_SRT_FILE  -est PATH_TO_EST_TXT -a -s {--n_to_align 500} --plot_2d --plot_3d  --save_plot SAVE_PLOT_PATH --output_folder PATH_OUTPUT_FOLDER  --plot_mode xy --flight_time FLIGHT_TIME_IN_SECS```


Follow the bellow command to evaluate your trajectory with a GT gps data in format `bag`

```python gps_evaluation_bag.py -b PATH_TO_ROS2_BAG_FOLDER -gt GT_TOPIC -n GT_TOPIC_PLUS_REST_TOPIC -out OUTPUT_FOLDER```

Example of bag evaluation
```python gps_evaluation_bag.py -b /home/<USER>/Workspace/data/recorded/rosbag2_2023_11_09-15_36_33 -gt /px4_interface/out/gps -n /px4_interface/out/gps /gnss_sync/out/gps /gnss_sync/out/status/enabled /gnss_sync/out/status/disabled /gnss_sync/out/status/start /gnss_sync/out/status/tracking /gnss_sync/out/status/tracking_lost -out ../output```

## Using the make pipeline

### Customer config yaml file
The customer config file is used for three purposes that require three different components:
- bag filtering with `ros2 bag convert` requires the `output_bags` section
- recording a specific list of topics for evaluation requires the `evaluation_topics` section
- running the bagfile evaluation script requires the `evaluation_topics` and `gt_topics` sections

Because all of the tools filter the `.yaml` file for their specific tags a single file can be used for the whole pipeline without any conflicts.

Follow the following format for a customer config file:
```
output_bags:
- uri: filtered
  topics: [
  /topic_name_A,
  /topic_name_B
  ] # list of topics to include in the output bag
  exclude: ^/(gnss_sync|vio) # regex of topics to exclude

evaluation_topics: [
  /topic_name_1, 
  /topic_name_2
]

gt_topic: [
  /gt_topic_name
]

```

for additional options see the ros2/rosbag2 and search for recording configuration or StorageOptions and RecordOptions.

Use either the `topics` or the `exclude` option.

### Bag filtering
To run bag filtering use:

```ros2 bag convert -i /input/bag/path -o /customer/config```

this will output a bag with a name and topics specified in the customer config file.

### Plotting from a ready bagfile
To run plotting on a bag that already contains all of the needed data use:

```make run-evaluation-plotter INPUT=/input/bag/path CUSTOMER=/customer/config```

this will generate and output the plots in the directory of the bagfile. This can be run as a standalone evaluation.

### Running the full evaluation pipeline
#### Warning: This is intended to be run together with spl-gnss-denied
To run full evaluation use:

```make run-evaluation-full INPUT=/input/bag/path OUTPUT=/output/bag/path CUSTOMER=/customer/config DELAY=60```

this will start recording a bag with topics from CUSTOMER to OUTPUT, play the bag from INPUT, call the GNSS cutoff service after the specified DELAY in seconds and finally generate and output the plots to the directory of the OUTPUT bagfile. This is inteded to be run from the spl-gnss-denied repo where it will also start spl-vio and gnss-sync.

## Typical use case example:
### Preprocessing
If running manually one will usually start with a bagfile. Start by moving you bag into the `deployment/data/bags` directory and checking that the topics in the bag and the customer file match:

```
cd evaluation/deployment/
cp bagfile data/bags/
ros2 bag info data/bags/bagfile
cat data/params/example.yaml
```

If needed update the customer file or create a new one for your purpose.

### Evaluation
Now run the plotting container:

```
make run-evaluation-plotter INPUT="./data/bags/bagfile" CUSTOMER="./data/params/customer.yaml"
```

use relative paths to allow for easy autocompletion, they are later stripped of the leading `.` by the script.

### Debugging
If you want to inspect if the evaluation was successful you can attach to the container with:

```
make attach-evaluation-plotter
```

and see if the evaluation script was executed properly or returned any errors.

### Rerunning gnss-sync and spl-vio on a recorded bag an evaluating the new result
For now this has to be done somewhat manually with the following steps:
- filter out the gnss-sync and spl-vio topic present in the bag using the exclude regex provided in the customer config example and the bag filtering command
- start the `gnss-sync` and `spl-vio` nodes manually
- run the full evaluation pipeline as described above with the filtered bag, appropriate customer config and gps cutoff delay, to run without a cutoff specify a delay longer then the bag length