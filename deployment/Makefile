# Name of the Repo
REPO_NAME=evaluation

# define all Docker images for the repo
appNameEvaluationPlotter ?= spl-evaluation-plotter
appNameEvaluationFull ?= spl-evaluation-full

# configure the Spleenlab Docker Registry
dockerServer ?= spleenlab-nas-3.synology.me
dockerPort ?= 40004

# path to store images
IMAGE_PATH ?= output

# define the output type of the Docker buildx process
DOCKER_OUTPUT_TYPE ?= docker

# define the target architecture to build the images for
BUILD_TARGET_ARCHITECTURES ?= linux/amd64

# define the target build stage within the Dockerfiles
BUILD_STAGE ?= release-stage

# define the Dockerfiles for all image to build within this repo
$(appNameEvaluationPlotter)_dockerfile = Dockerfile
$(appNameEvaluationFull)_dockerfile = Dockerfile

# define all images to pull from registry
PULL_TARGETS =

# define all images to build within this deployment pipeline
BUILD_TARGETS = $(appNameEvaluationPlotter) $(appNameEvaluationFull)

# define all images with automated unit tests
TEST_TARGETS =

# define all run configurations (every configuration needs a separate app_[configuration name].yaml
RUN_TARGETS=run-evaluation-plotter run-evaluation-full

#Use the standard base image as written in the Dockerfile by default
GET_BASE_IMAGE ?= $(addprefix get-,$(addsuffix -base-image, $(BUILD_TARGETS)))
$(GET_BASE_IMAGE):
	$(eval baseImage:=)

# defines all docker tags for this architecture
get-docker-tag:
	$(eval dockerTag=amd64)
	$(eval $(appNameEvaluationPlotter)_TAG=latest)
	$(eval $(appNameEvaluationFull)_TAG=latest)
	$(eval EXPORT_TAGS= \
    				  export IMAGE_TAG_EVALUATION_PLOTTER=$($(appNameEvaluationPlotter)_TAG); \
    				  export IMAGE_TAG_EVALUATION_FULL=$($(appNameEvaluationFull)_TAG); )

get-evaluation-plotter-preprocess: get-docker-tag
	$(eval PREPROCESS= \
		export INPUT_BAG=${INPUT}; \
 		export CUSTOMER_CONFIG=${CUSTOMER}; )

get-evaluation-full-preprocess: get-docker-tag
	$(eval PREPROCESS= \
		export INPUT_BAG=${INPUT}; \
		export OUTPUT_BAG=${OUTPUT}; \
		export CUSTOMER_CONFIG=${CUSTOMER}; \
		export DELAY=${DELAY}; )

# include the default make steps
include Makefile.common

# build all release images
build: $(BUILD)

# pull all images not built within repo
pull: $(PULL)

# pull all images (including the ones built within container)
pull-all: $(PULL_ALL)
