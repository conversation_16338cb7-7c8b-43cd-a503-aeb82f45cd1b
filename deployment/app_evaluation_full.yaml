version: '3.4'
services:
  spl-evaluation-full:
    image: spl-evaluation-full:${IMAGE_TAG_EVALUATION_FULL}
    container_name: spl-evaluation-full
    restart: "no"
    network_mode: host
    tty: true
    volumes:
      - "./data:/data"
    environment:
      - ROS_DOMAIN_ID=${ROS_DOMAIN_ID}
      - INPUT_BAG=${INPUT_BAG}
      - OUTPUT_BAG=${OUTPUT_BAG}
      - CUSTOMER_CONFIG=${CUSTOMER_CONFIG}
      - DELAY=${DELAY}
    entrypoint: 
      - "/bin/bash"
      - "-c"
      - "/script/run_evaluation_full.sh"
