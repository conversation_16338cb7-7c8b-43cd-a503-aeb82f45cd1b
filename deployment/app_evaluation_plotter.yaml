version: '3.4'
services:
  spl-evaluation-plotter:
    image: spl-evaluation-plotter:${IMAGE_TAG_EVALUATION_PLOTTER}
    container_name: spl-evaluation-plotter
    restart: "no"
    network_mode: host
    tty: true
    volumes:
      - "./data:/data"
    environment:
      - ROS_DOMAIN_ID=${ROS_DOMAIN_ID}
      - INPUT_BAG=${INPUT_BAG}
      - CUSTOMER_CONFIG=${CUSTOMER_CONFIG}
    entrypoint:
      - "/bin/bash"
      - "-c"
      - "/script/run_plotter.sh"
