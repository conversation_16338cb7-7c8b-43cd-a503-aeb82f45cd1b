ARG BASE_IMAGE=ros:humble-ros-core
FROM $BASE_IMAGE as base-stage

# Setup environment
ENV LANG C.UTF-8
ENV LC_ALL C.UTF-8
ENV DEBIAN_FRONTEND noninteractive
ENV SHELL /bin/bash
ENV ROS_LOG_DIR /data/log
ENV ROS_DISTRO humble

WORKDIR /buildroot

#install necessary dependencies
COPY deployment/script/install_dependencies.sh /buildroot/
COPY deployment/script/run_command_in_environment.sh /buildroot/
RUN /buildroot/install_dependencies.sh

COPY ../requirements.txt /buildroot/
RUN python3 -m pip install --upgrade pip
RUN python3 -m pip install -r /buildroot/requirements.txt

FROM base-stage as release-stage

ENV RMW_IMPLEMENTATION rmw_cyclonedds_cpp

RUN echo "source /opt/ros/${ROS_DISTRO}/setup.bash" >> ~/.bashrc

#copy files necessary for the evaluation pipeline
COPY deployment/script/run_evaluation_full.sh /script/
COPY deployment/script/evaluation_full.sh /script/
COPY deployment/script/run_plotter.sh /script/
COPY ../odometry /odometry

ENTRYPOINT ["/buildroot/run_command_in_environment.sh"]
