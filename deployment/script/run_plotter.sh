#!/bin/bash

SESSION_NAME="plotter"

# Obtain the paths inside the container from the input paths
INPUT_PATH="/data/bags/"${INPUT_BAG##*bags/}
CUSTOMER_CONFIG_PATH="/data/params/"${CUSTOMER_CONFIG##*params/}

ERROR_MSG="Evaluation returned an error."

tmux new-session -d -s $SESSION_NAME
tmux send-keys "python3 /odometry/gps_evaluation_bag.py -b ${INPUT_PATH} -c ${CUSTOMER_CONFIG_PATH} -out ${INPUT_PATH} && tmux wait -S $SESSION_NAME || echo ${ERROR_MSG}" Enter
tmux wait $SESSION_NAME
