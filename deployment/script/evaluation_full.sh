#!/bin/bash
set -euxo pipefail

# Enter the directory containing the bagfiles
cd /data/bags

# Obtain the paths inside the container from the input paths
INPUT_PATH="/data/bags/"${INPUT_BAG##*bags/}
OUTPUT_PATH="/data/bags/"${OUTPUT_BAG##*bags/}
CUSTOMER_CONFIG_PATH="/data/params/"${CUSTOMER_CONFIG##*params/}

# Test if input bag and customer config exist
test -e "${INPUT_PATH}"
test -e "${CUSTOMER_CONFIG_PATH}"

# Delete the output bag if it exists, otherwise bag recording will throw an error
test -e "${OUTPUT_PATH}" && rm -r "${OUTPUT_PATH}"

# If delay was set then run the GPS cutoff service after a delay given in seconds
if [ -n "${DELAY}" ]; then
	sleep "${DELAY}" && ros2 service call /gnss_sync_node/disable_gnss_input std_srvs/srv/Empty {} &
fi

# Read a list of topics from yaml, pass them via xargs and run bag recording of these topics in the background
python3 -c "import yaml; print(\" \".join(yaml.safe_load(open(\"${CUSTOMER_CONFIG_PATH}\"))[\"evaluation_topics\"]))" | xargs ros2 bag record -o "${OUTPUT_PATH}" &

# Play a bag in the foreground and wait for it to finish playing before continuing
ros2 bag play "${INPUT_PATH}"

# Kill the bag recording process to end the recording with end of playback
pkill -f "ros2 bag record"

# Test if the recording created a valid bag
ros2 bag info "${OUTPUT_PATH}"

# Run evaluation on the recorded output bag
python3 /odometry/gps_evaluation_bag.py -b "${OUTPUT_PATH}" -c "${CUSTOMER_CONFIG_PATH}" -out "${OUTPUT_PATH}"
