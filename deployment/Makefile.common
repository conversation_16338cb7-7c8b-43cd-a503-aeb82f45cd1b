#TODO: Define the following variables before including this file!
# BUILD_TARGETS: All containers, that need to be built
# PULL_TARGETS: All containers, that need to be pulled from the registry
# For all BUILD_TARGETS: define a variable $(TARGET)_dockerfile
# get_docker_tag: a target, that provides docker tags for all the targets

BUILD = $(addprefix build-, $(BUILD_TARGETS))
BUILD_DEVELOPMENT = $(addprefix build-development-, $(BUILD_TARGETS))
PULL = $(addprefix pull-, $(PULL_TARGETS))
#Add all the build targets so they can be pulled instead of built
PULL_ALL = $(PULL) $(addprefix pull-, $(BUILD_TARGETS))

LOAD = $(addprefix load-, $(BUILD_TARGETS) $(PULL_TARGETS))
SAVE = $(addprefix save-, $(BUILD_TARGETS) $(PULL_TARGETS))
PUSH = $(addprefix push-, $(BUILD_TARGETS))
EXPORT_SOURCE = $(addprefix export-source-, ${BUILD_TARGETS})
CONTAINER-VERSION = ${addprefix container-version-, $(DELIVERY_TARGETS)}

ATTACH = $(addprefix attach-, $(BUILD_TARGETS) $(PULL_TARGETS))
TEST = $(addprefix test-, $(TEST_TARGETS))
STOP = $(subst run,stop, $(RUN_TARGETS))

# build an image from the Dockerfile within the repo in release mode
.SECONDEXPANSION:
$(BUILD): get-docker-tag get-$$(subst build-,,$$@)-base-image
	$(eval appName := $(subst build-,, $@))
	if [ "$(baseImage)" != "" ]; then \
	    BASE_IMAGE_ARG="--build-arg BASE_IMAGE=$(dockerServer):$(dockerPort)/$(baseImage)"; \
	fi; \
	docker buildx build  -f $($(appName)_dockerfile) .. --target $(BUILD_STAGE) -t $(dockerServer):$(dockerPort)/$(appName):$($(appName)_TAG) $${BASE_IMAGE_ARG} --platform $(BUILD_TARGET_ARCHITECTURES) --output type=$(DOCKER_OUTPUT_TYPE)
	if [ "$(DOCKER_OUTPUT_TYPE)" = "docker" ]; then docker tag $(dockerServer):$(dockerPort)/$(appName):$($(appName)_TAG) $(appName):$($(appName)_TAG); fi

# build an image from the Dockerfile within the repo in development mode including sources
.SECONDEXPANSION:
$(BUILD_DEVELOPMENT): get-docker-tag get-$$(subst build-development-,,$$@)-base-image
	$(eval appName := $(subst build-development-,, $@))
	if [ "$(baseImage)" != "" ]; then \
		BASE_IMAGE_ARG="--build-arg BASE_IMAGE=$(dockerServer):$(dockerPort)/$(baseImage)"; \
	fi; \
	docker buildx build -f $($(appName)_dockerfile) .. --target development-stage -t $(appName):development $${BASE_IMAGE_ARG} --platform $(BUILD_TARGET_ARCHITECTURES) --output type=docker
	@echo "For using as toolchain, select $(appName):development as image and add $(DEVELOPMENT_RUN_OPTIONS) as run options under container settings."

# save the Docker image from local Docker cache to disk
.SECONDEXPANSION:
$(SAVE): get-docker-tag
	$(eval appName := $(subst save-,, $@))
	mkdir -p ${IMAGE_PATH}
	docker save $(appName):$($(appName)_TAG) | tqdm --bytes --total $$(docker image inspect $(appName):$($(appName)_TAG) --format='{{.Size}}') | gzip > $(IMAGE_PATH)/$(appName).image

# load an Docker image from disk into local Docker cache
$(LOAD):
	$(eval appName := $(subst load-,, $@))
	pv $(IMAGE_PATH)/$(appName).image | docker load

# pull all image from Spleenlab Docker registry (including those which are built within the repo)
$(PULL_ALL): get-docker-tag
	$(eval appName := $(subst pull-,, $@))
	docker pull $(dockerServer):$(dockerPort)/$(appName):$($(appName)_TAG)
	docker tag $(dockerServer):$(dockerPort)/$(appName):$($(appName)_TAG) $(appName):$($(appName)_TAG)

# push a Docker image to the Spleenlab Docker registry
$(PUSH): get-docker-tag
	$(eval appName := $(subst push-,, $@))
	docker tag $(appName):$($(appName)_TAG) $(dockerServer):$(dockerPort)/$(appName):$($(appName)_TAG)
	docker push $(dockerServer):$(dockerPort)/$(appName):$($(appName)_TAG)

# attach to a running Docker container
$(ATTACH):
	$(eval appName := $(subst attach-,, $@))
	docker exec -it $(appName) /bin/bash -ic "tmux a"

# run unit tests
.SECONDEXPANSION:
$(TEST): get-docker-tag get-$$(subst test-,,$$@)-base-image
	$(eval appName := $(subst test-,, $@))
	if [ "$(baseImage)" != "" ]; then \
		BASE_IMAGE_ARG="--build-arg BASE_IMAGE=$(dockerServer):$(dockerPort)/$(baseImage)"; \
	fi; \
	docker buildx build  -f $($(appName)_dockerfile) .. --target build-stage -t $(appName):test $${BASE_IMAGE_ARG} --platform $(BUILD_TARGET_ARCHITECTURES) --output type=docker
	docker run -i -v "${PWD}/test/$(appName):/test" --entrypoint "/bin/bash" $(appName):test -ic "/test/start_test.sh"

# run a defined pipeline using docker compose (needs an app.yaml)
.SECONDEXPANSION:
$(RUN_TARGETS): get$$(subst run,,$$@)-preprocess
	$(eval appYaml := app$(subst -,_,$(subst run,,$@))$(HITL_DEVICE).yaml)
	$(PREPROCESS) \
	$(EXPORT_TAGS) \
	if [ -z ${ROS_DOMAIN_ID} ]; then echo "ROS_DOMAIN_ID not set! Set it to 0."; export ROS_DOMAIN_ID=0; fi; \
	docker-compose -f $(appYaml) $(PROFILE) up -d --force-recreate

# stop a running pipeline using docker compose (needs an app.yaml)
.SECONDEXPANSION:
$(STOP): get$$(subst stop,,$$@)-preprocess
	$(eval appYaml := app$(subst -,_,$(subst stop,,$@))$(HITL_DEVICE).yaml)
	$(EXPORT_TAGS) \
	docker-compose -f $(appYaml) down

# export sources of all installed dependencies for licensing compliance
.SECONDEXPANSION:
$(EXPORT_SOURCE): get-docker-tag
	$(eval appName := $(subst export-source-,, $@))
	make build-$(appName) BUILD_STAGE=source-release-stage $(appName)_TAG=source

	# make sure there is no container with the same name
	-@docker rm $(appName)-source
	docker create --name $(appName)-source $(appName):source
	mkdir -p $(IMAGE_PATH)
	docker export -o $(IMAGE_PATH)/$(appName)_$($(appName)_version)-source.tar $(appName)-source
	docker rm $(appName)-source

# print version information of container based on Docker hash
.SECONDEXPANSION:
$(CONTAINER-VERSION): get-docker-tag
	$(eval appName := $(subst container-version-,, $@))
	@docker image ls --no-trunc --format "{{.Repository}}:{{.Tag}} {{.ID}}" $(appName):$($(appName)_TAG)
