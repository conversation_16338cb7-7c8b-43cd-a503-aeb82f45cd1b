import argparse
import logging.config
import os

from utils.bag_loader import BagLoader
from utils.data_loader import DataLoader
from utils.visualization import (
    geo_map_single,
    geo_map_tracking,
    plot_xyz_tracking_error,
)


def parser():
    # create the top-level parser
    parsers = argparse.ArgumentParser(description="GPS Evaluation from bag file")
    input_opts = parsers.add_argument_group("input")
    input_opts.add_argument("-b", "--bag_file", type=str, default="", help="Path bag file")
    input_opts.add_argument("-gt", "--gt_topic", type=str, default="", help="GPS gt topic name")
    input_opts.add_argument(
        "-n",
        "--topic_names",
        nargs="+",
        default=[],
        help="List of required topic names",
    )
    input_opts.add_argument("-c", "--config_file", type=str, default="", help="Config file with topic names")
    input_opts.add_argument("-f", "--xlsm_file", type=str, default="", help="Path to XLSM file")

    algo_opts = parsers.add_argument_group("algorithm options")
    algo_opts.add_argument(
        "-r",
        "--pose_relation",
        default="trans_part",
        help="pose relation on which the RPE is based",
        choices=[
            "full",
            "trans_part",
            "rot_part",
            "angle_deg",
            "angle_rad",
            "point_distance",
            "point_distance_error_ratio",
        ],
    )
    algo_opts.add_argument("-a", "--align", help="alignment with Umeyama's method", action="store_true")
    algo_opts.add_argument(
        "-s",
        "--correct_scale",
        action="store_true",
        help="correct scale with Umeyama's method",
    )
    algo_opts.add_argument(
        "--n_to_align",
        help="the number of poses to use for Umeyama alignment, " "counted from the start (default: all)",
        default=-1,
        type=int,
    )
    algo_opts.add_argument(
        "--align_origin",
        help="align the trajectory origin to the origin of the reference " "trajectory",
        action="store_true",
    )

    algo_opts.add_argument(
        "-d",
        "--delta",
        type=float,
        default=1,
        help="""delta parameter represents the distance interval used to match poses for relative pose estimation."
        "It defines the spatial gap between consecutive poses that should be considered when calculating the relative"
        "transformation or movement from one pose to another""",
    )
    algo_opts.add_argument(
        "-t",
        "--delta_tol",
        type=float,
        default=0.1,
        help="relative delta tolerance for all_pairs mode",
    )
    algo_opts.add_argument(
        "-u",
        "--delta_unit",
        default="f",
        help="unit of delta - `f` (frames), `d` (deg), `r` (rad), `m`(meters)",
        choices=["f", "d", "r", "m"],
    )

    output_opts = parsers.add_argument_group("output options")
    output_opts.add_argument(
        "-no_map",
        "--disable_plot_geo_map",
        help="disable geographic map plot",
        action="store_true",
    )
    output_opts.add_argument(
        "-no_freq",
        "--disable_plot_frequency",
        help="disable frequency plot",
        action="store_true",
    )
    output_opts.add_argument("-no_2d", "--disable_plot_2d", help="disable draw 2D plot", action="store_true")
    output_opts.add_argument("-no_3d", "--disable_plot_3d", help="disable draw 3D plot", action="store_true")
    output_opts.add_argument("-out", "--output_folder", default="", help="path to save result")
    output_opts.add_argument("-show", "--show_plots", help="show plots in addition to saving them", action="store_true")

    args, _ = parsers.parse_known_args()
    return args


def load_logger():
    # Get the current file's path
    current_folder = os.path.dirname(os.path.abspath(__file__))

    # Construct the file location relative to the current folder or main Python module folder path
    file_location = os.path.join(current_folder, "logging.conf")

    # Use the modified file location in the fileConfig function
    logging.config.fileConfig(file_location)


def main():
    # Load logger
    load_logger()
    logger = logging.getLogger("odom_eval")
    args = parser()

    if not os.path.exists(args.output_folder):
        os.makedirs(args.output_folder)

    # Load the config file
    bl = BagLoader()
    logger.info("Reading topic names from config file ...")
    topic_dict, topic_names = bl.read_config_file(args.config_file)

    # Load the gps topics from bag file
    logger.info("Reading data from bag file ...")
    topic_msgs, topic_timestamps = bl.read_topics_bag(args.bag_file, topic_names)

    quantum_msg_np = topic_msgs[topic_dict["gt_topic"]]

    bag_filename = os.path.basename(args.bag_file)

    dl = DataLoader()
    tracking_np_wgs84 = dl.load_xlsm_tracking_data(
        file_path=args.xlsm_file,
        gt_msg_np=quantum_msg_np,
    )
    # Draw geographical plots on open street map
    if not args.disable_plot_geo_map:
        out_geo_filename = os.path.join(args.output_folder, "geo_tracking")
        geo_map_tracking(
            tracking_np_wgs84=tracking_np_wgs84,
            gt_np_wgs84=quantum_msg_np,
            bag_filename=bag_filename,
            filename=out_geo_filename,
            show_plot=args.show_plots,
        )

        out_quan_filename = os.path.join(args.output_folder, "geo_quantum")
        geo_map_single(
            traj=quantum_msg_np,
            filename=out_quan_filename,
            title="GPS",
            color="RoyalBlue",
            show_plot=args.show_plots,
        )

        out_track_filename = os.path.join(args.output_folder, "geo_tracking")
        geo_map_single(
            traj=tracking_np_wgs84,
            filename=out_track_filename,
            title="KTH",
            color="LimeGreen",
            show_plot=args.show_plots,
        )

        error_filename = os.path.join(args.output_folder, "error")
        plot_xyz_tracking_error(
            tracking_np=tracking_np_wgs84,
            gt_np=quantum_msg_np,
            title="",
            filename=error_filename,
            show_plot=args.show_plots,
        )


if __name__ == "__main__":
    main()
