import argparse
import logging.config
import os

from evo.core.trajectory import PoseTrajectory3D
from metric.pose_metric import RPEMetric
from utils.bag_loader import BagLoader
from utils.data_loader import DataLoader
from utils.point3d_ops import (
    sync_gt_with_estimated_vo_msgs,
)
from utils.visualization import plot_3d_single


def parser():
    # create the top-level parser
    parsers = argparse.ArgumentParser(description="GPS Evaluation from bag file")
    input_opts = parsers.add_argument_group("input")
    input_opts.add_argument("-b", "--bag_file", type=str, default="", help="Path bag file")
    input_opts.add_argument("-csv", "--csv_file", type=str, default="", help="Path csv file")
    input_opts.add_argument("-c", "--config_file", type=str, default="", help="Config file with topic names")

    algo_opts = parsers.add_argument_group("algorithm options")
    algo_opts.add_argument(
        "-r",
        "--pose_relation",
        default="trans_part",
        help="pose relation on which the RPE is based",
        choices=[
            "full",
            "trans_part",
            "rot_part",
            "angle_deg",
            "angle_rad",
            "point_distance",
            "point_distance_error_ratio",
        ],
    )
    algo_opts.add_argument("-a", "--align", help="alignment with Umeyama's method", action="store_true")
    algo_opts.add_argument(
        "-s",
        "--correct_scale",
        action="store_true",
        help="correct scale with Umeyama's method",
    )
    algo_opts.add_argument(
        "--n_to_align",
        help="the number of poses to use for Umeyama alignment, " "counted from the start (default: all)",
        default=-1,
        type=int,
    )
    algo_opts.add_argument(
        "--align_origin",
        help="align the trajectory origin to the origin of the reference " "trajectory",
        action="store_true",
    )

    algo_opts.add_argument(
        "-d",
        "--delta",
        type=float,
        default=1,
        help="""delta parameter represents the distance interval used to match poses for relative pose estimation."
        "It defines the spatial gap between consecutive poses that should be considered when calculating the relative"
        "transformation or movement from one pose to another""",
    )
    algo_opts.add_argument(
        "-t",
        "--delta_tol",
        type=float,
        default=0.1,
        help="relative delta tolerance for all_pairs mode",
    )
    algo_opts.add_argument(
        "-u",
        "--delta_unit",
        default="f",
        help="unit of delta - `f` (frames), `d` (deg), `r` (rad), `m`(meters)",
        choices=["f", "d", "r", "m"],
    )

    output_opts = parsers.add_argument_group("output options")
    output_opts.add_argument(
        "-no_map",
        "--disable_plot_geo_map",
        help="disable geographic map plot",
        action="store_true",
    )
    output_opts.add_argument(
        "-no_freq",
        "--disable_plot_frequency",
        help="disable frequency plot",
        action="store_true",
    )
    output_opts.add_argument("-no_2d", "--disable_plot_2d", help="disable draw 2D plot", action="store_true")
    output_opts.add_argument("-no_3d", "--disable_plot_3d", help="disable draw 3D plot", action="store_true")
    output_opts.add_argument("-out", "--output_folder", default="", help="path to save result")
    output_opts.add_argument("-show", "--show_plots", help="show plots in addition to saving them", action="store_true")

    args, _ = parsers.parse_known_args()
    return args


def load_logger():
    # Get the current file's path
    current_folder = os.path.dirname(os.path.abspath(__file__))

    # Construct the file location relative to the current folder or main Python module folder path
    file_location = os.path.join(current_folder, "logging.conf")

    # Use the modified file location in the fileConfig function
    logging.config.fileConfig(file_location)


def main():
    # Load logger
    load_logger()
    logger = logging.getLogger("odom_eval")
    args = parser()

    if not os.path.exists(args.output_folder):
        os.makedirs(args.output_folder)

    # Load the config file
    bag_loader = BagLoader()
    logger.info("Reading topic names from config file ...")
    topic_dict, topic_names = bag_loader.read_config_file(args.config_file)

    # Load the gps topics from bag file
    logger.info("Reading data from bag file ...")
    topic_msgs, topic_timestamps = bag_loader.read_topics_bag(args.bag_file, topic_names)

    data_loader = DataLoader()
    est_abs_trajectory = data_loader.load_tum_csv(args.csv_file)

    plot_3d_single(est_abs_trajectory[:, :4], "red", filename=args.output_folder + "3d_plot")

    # Sync the gt and estimated gps data. Convert them to cartesian
    gt_traj_enu, est_traj_enu = sync_gt_with_estimated_vo_msgs(
        gt_msg_np=topic_msgs[topic_dict["gt_topic"]], est_msg_np=est_abs_trajectory
    )

    logger.info("GT information %s", gt_traj_enu)
    logger.info("ES information %s", est_traj_enu)

    CUT_OFF_POSES_BEGIN = 0
    CUT_OFF_POSES_END = 1

    if (
        gt_traj_enu.positions_xyz.shape[0] > CUT_OFF_POSES_BEGIN + CUT_OFF_POSES_END
        and est_traj_enu.positions_xyz.shape[0] > CUT_OFF_POSES_BEGIN + CUT_OFF_POSES_END
    ):
        gt_traj_enu = PoseTrajectory3D(
            positions_xyz=gt_traj_enu.positions_xyz[CUT_OFF_POSES_BEGIN:-CUT_OFF_POSES_END],
            orientations_quat_wxyz=gt_traj_enu.orientations_quat_wxyz[CUT_OFF_POSES_BEGIN:-CUT_OFF_POSES_END],
            timestamps=gt_traj_enu.timestamps[CUT_OFF_POSES_BEGIN:-CUT_OFF_POSES_END],
        )

        est_traj_enu = PoseTrajectory3D(
            positions_xyz=est_traj_enu.positions_xyz[CUT_OFF_POSES_BEGIN:-CUT_OFF_POSES_END],
            orientations_quat_wxyz=est_traj_enu.orientations_quat_wxyz[CUT_OFF_POSES_BEGIN:-CUT_OFF_POSES_END],
            timestamps=est_traj_enu.timestamps[CUT_OFF_POSES_BEGIN:-CUT_OFF_POSES_END],
        )
    else:
        logger.warning("Not enough poses to cut off the first and last 150 poses.")

    logger.info("GT information %s", gt_traj_enu)
    logger.info("ES information %s", est_traj_enu)

    # Compute relative pose error
    rpe_metric = RPEMetric(args, gt_traj_enu, est_traj_enu)
    rpe_result = rpe_metric.compute(align=True, correct_scale=True, n_to_align=-1, align_origin=False)
    rpe_metric.plot_3d(topic_msgs=topic_msgs, topic_dict=topic_dict, bag_filename=args.csv_file)
    rpe_metric.plot_2d(
        topic_msgs=topic_msgs,
        topic_dict=topic_dict,
        bag_filename=args.csv_file,
        output_folder=args.output_folder,
    )

    result_text_filename = os.path.join(args.output_folder, "rpe_result.txt")
    with open(result_text_filename, "w", encoding="utf-8") as f:
        f.write(str(rpe_result))


if __name__ == "__main__":
    main()
