# Batch Odometry Evaluation Script

This script (`odometry_evaluation_batch.py`) extends the functionality of `odometry_evaluation_file.py` to process multiple odometry CSV files in batch mode against a single ground truth file.

## Features

- **Batch Processing**: Evaluates multiple odometry files against one ground truth file
- **Organized Output**: Creates numbered subfolders (00, 01, 02, ...) for each evaluation
- **Complete Results**: Each subfolder contains all plots, text results, and a copy of the input file
- **Efficient**: Loads ground truth only once and reuses it for all evaluations
- **Flexible**: Supports custom file patterns and various evaluation options

## Usage

### Basic Command

```bash
python odometry_evaluation_batch.py -gt <ground_truth_file> -est_folder <input_folder> -out <output_folder>
```

### Required Arguments

- `-gt, --gt_file`: Path to ground truth GPS file (.csv or .txt)
- `-est_folder, --est_folder`: Path to folder containing estimated odometry CSV files
- `-out, --output_folder`: Path to output folder for results

### Optional Arguments

- `-a, --align`: Enable alignment with <PERSON><PERSON><PERSON>'s method
- `-s, --correct_scale`: Correct scale with <PERSON><PERSON><PERSON>'s method
- `--align_origin`: Align trajectory origin to reference trajectory origin
- `-no_3d, --disable_plot_3d`: Disable 3D plot generation
- `-no_vel, --disable_plot_velocity`: Disable velocity plots
- `-show, --show_plots`: Show plots in addition to saving them
- `-pattern, --file_pattern`: Pattern to match odometry files (default: "odometry_*.csv")

### Example Usage

```bash
# Basic evaluation
python odometry_evaluation_batch.py \
    -gt /path/to/ground_truth.csv \
    -est_folder /path/to/odometry_files \
    -out /path/to/results

# With alignment and plot display
python odometry_evaluation_batch.py \
    -gt /path/to/ground_truth.csv \
    -est_folder /path/to/odometry_files \
    -out /path/to/results \
    --align \
    --show_plots

# Custom file pattern
python odometry_evaluation_batch.py \
    -gt /path/to/ground_truth.csv \
    -est_folder /path/to/odometry_files \
    -out /path/to/results \
    -pattern "trajectory_*.csv"
```

## Input Requirements

### Ground Truth File
- Format: CSV or TXT file
- Must contain timestamp, position (x, y, z), and orientation (w, x, y, z) data
- Supported formats: TUM format

### Estimated Trajectory Files
- Format: CSV or TXT files matching the specified pattern
- Default pattern: `odometry_*.csv`
- Must contain timestamp, position (x, y, z), and orientation (w, x, y, z) data
- Supported formats: TUM format

## Output Structure

The script creates numbered subfolders in the output directory:

```
output_folder/
├── 00/
│   ├── ape_result.txt                    # APE evaluation results
│   ├── trajectory_3d_comparison.png      # 3D trajectory comparison plot
│   ├── multiple_trajectories.png         # Multiple trajectory visualization
│   ├── translation_drift_per_sec_rpe.png # Translation drift plot
│   └── odometry_01.csv                   # Copy of input odometry file
├── 01/
│   ├── ape_result.txt
│   ├── trajectory_3d_comparison.png
│   ├── multiple_trajectories.png
│   ├── translation_drift_per_sec_rpe.png
│   └── odometry_02.csv
└── ...
```

## Output Files Description

### ape_result.txt
Contains the Absolute Pose Error (APE) evaluation results including:
- Mean, median, and standard deviation of errors
- Root Mean Square Error (RMSE)
- Maximum and minimum errors
- Error statistics over time

### trajectory_3d_comparison.png
3D visualization showing:
- Ground truth trajectory (blue)
- Estimated trajectory (red)
- Error magnitude color-coded on the estimated trajectory

### multiple_trajectories.png
Side-by-side comparison of:
- Ground truth trajectory
- Estimated trajectory
- Error visualization

### translation_drift_per_sec_rpe.png
Translation drift analysis showing:
- Drift per second over time
- Statistical drift metrics

## Error Handling

The script includes robust error handling:
- Continues processing even if individual files fail
- Logs errors for failed files
- Reports success/failure statistics at the end
- Creates output folders automatically

## Performance Considerations

- Ground truth is loaded only once and reused for all evaluations
- Each file is processed independently
- Memory usage scales with the largest trajectory file
- Processing time scales linearly with the number of files

## Dependencies

The script requires the same dependencies as `odometry_evaluation_file.py`:
- numpy
- pandas
- matplotlib
- evo (for trajectory evaluation)
- Custom modules: `metric.pose_metric`, `utils.data_loader`, `utils.visualization`

## Troubleshooting

### Common Issues

1. **No files found**: Check the file pattern and ensure files exist in the input folder
2. **Memory errors**: Process files in smaller batches if dealing with very large trajectories
3. **Plot display issues**: Use `--disable_plot_3d` if running on headless systems
4. **Permission errors**: Ensure write permissions for the output folder

### Debug Mode

Enable verbose logging by modifying the logging configuration in `logging.conf` or by setting the log level programmatically.

## Comparison with Single File Evaluation

| Feature | Single File (`odometry_evaluation_file.py`) | Batch (`odometry_evaluation_batch.py`) |
|---------|---------------------------------------------|----------------------------------------|
| Input | Single file | Multiple files from folder |
| Ground Truth | Loaded per evaluation | Loaded once, reused |
| Output | Single folder | Multiple numbered subfolders |
| Organization | Manual | Automatic |
| Efficiency | Lower for multiple files | Higher for multiple files |
| Error Handling | Stops on error | Continues on individual file errors |
