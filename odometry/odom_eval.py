import argparse
import logging.config

from evo.tools.settings import SETTINGS
from metric.pose_metric import RPEMetric
from utils.data_loader import DataLoader


def parser():
    # create the top-level parser
    parser = argparse.ArgumentParser(description="Odometry Evaluation")
    input_opts = parser.add_argument_group("input")
    input_opts.add_argument("-gt", "--gt_file", type=str, default="", help="Ground-truth csv file")
    input_opts.add_argument("-est", "--est_file", type=str, default="", help="Estimated csv file")

    algo_opts = parser.add_argument_group("algorithm options")
    algo_opts.add_argument(
        "-r",
        "--pose_relation",
        default="trans_part",
        help="pose relation on which the RPE is based",
        choices=[
            "full",
            "trans_part",
            "rot_part",
            "angle_deg",
            "angle_rad",
            "point_distance",
            "point_distance_error_ratio",
        ],
    )
    algo_opts.add_argument("-a", "--align", help="alignment with <PERSON><PERSON><PERSON>'s method (no scale)", action="store_true")
    algo_opts.add_argument("-s", "--correct_scale", action="store_true", help="correct scale with Umeyama's method")
    algo_opts.add_argument(
        "--n_to_align",
        help="the number of poses to use for Umeyama alignment, " "counted from the start (default: all)",
        default=-1,
        type=int,
    )
    algo_opts.add_argument(
        "--align_origin",
        help="align the trajectory origin to the origin of the reference " "trajectory",
        action="store_true",
    )
    algo_opts.add_argument("-d", "--delta", type=float, default=1, help="delta between relative poses")
    algo_opts.add_argument(
        "-t", "--delta_tol", type=float, default=0.1, help="relative delta tolerance for all_pairs mode"
    )
    algo_opts.add_argument(
        "-u",
        "--delta_unit",
        default="f",
        help="unit of delta - `f` (frames), `d` (deg), `r` (rad), `m`(meters)",
        choices=["f", "d", "r", "m"],
    )
    algo_opts.add_argument(
        "--all_pairs",
        action="store_true",
        help="use all pairs instead of consecutive pairs (disables plot)",
    )

    output_opts = parser.add_argument_group("output options")
    output_opts.add_argument(
        "-p",
        "--plot",
        action="store_true",
        help="show plot window",
    )
    output_opts.add_argument(
        "--plot_mode",
        default=SETTINGS.plot_mode_default,
        help="the axes for plot projection",
        choices=["xy", "xz", "yx", "yz", "zx", "zy", "xyz"],
    )
    output_opts.add_argument(
        "--plot_x_dimension",
        choices=["index", "seconds", "distances"],
        default="seconds",
        help="dimension that is used on the x-axis of the raw value plot"
        "(default: seconds, or index if no timestamps are present)",
    )
    output_opts.add_argument(
        "--plot_colormap_max",
        type=float,
        help="the upper bound used for the color map plot " "(default: maximum error value)",
    )
    output_opts.add_argument(
        "--plot_colormap_min",
        type=float,
        help="the lower bound used for the color map plot " "(default: minimum error value)",
    )
    output_opts.add_argument(
        "--plot_colormap_max_percentile",
        type=float,
        help="percentile of the error distribution to be used "
        "as the upper bound of the color map plot "
        "(in %%, overrides --plot_colormap_max)",
    )
    output_opts.add_argument(
        "--plot_full_ref",
        action="store_true",
        help="plot the full, unsynchronized reference trajectory",
    )
    output_opts.add_argument(
        "--ros_map_yaml",
        help="yaml file of an ROS 2D map image (.pgm/.png)" " that will be drawn into the plot",
        default=None,
    )
    output_opts.add_argument("--save_plot", default="", help="path to save plot")
    output_opts.add_argument("--serialize_plot", default=None, help="path to serialize plot (experimental)")
    output_opts.add_argument("--save_results", help=".zip file path to store results")
    output_opts.add_argument("--logfile", help="Local logfile path.", default=None)

    output_opts.add_argument("--no_warnings", action="store_true", help="no warnings requiring user confirmation")
    output_opts.add_argument("-v", "--verbose", action="store_true", help="verbose output")
    output_opts.add_argument("--silent", action="store_true", help="don't print any output")
    output_opts.add_argument("--debug", action="store_true", help="verbose output with additional debug info")
    output_opts.add_argument("-c", "--config", help=".json file with parameters (priority over command line args)")

    output_opts.add_argument("-of", "--output_txt_file", type=str, default="", help="output txt file")

    args, _ = parser.parse_known_args()
    return args


def main():
    logging.config.fileConfig("./logging.conf")
    logger = logging.getLogger(__name__)

    args = parser()

    dl = DataLoader()
    logger.info("Reading GT and ES data from the csv files ...")
    gt_data, gt_timestamps = dl.load_csv_data(args.gt_file, "GT")
    logger.info("GT information %s", gt_data)

    es_data, _ = dl.load_csv_data(args.es_file, "ES", gt_timestamps)
    logger.info("ES information %s", es_data)

    rpe_metric = RPEMetric(args, gt_data, es_data)
    rpe_result = rpe_metric.compute()
    rpe_metric.plot_2d()
    rpe_metric.plot_3d()

    with open(args.output_txt_file, "w") as f:
        f.write(str(rpe_result))


if __name__ == "__main__":
    main()
