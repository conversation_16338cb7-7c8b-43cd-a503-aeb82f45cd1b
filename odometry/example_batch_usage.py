#!/usr/bin/env python3
"""
Example usage of the batch odometry evaluation script.

This script demonstrates how to use odometry_evaluation_batch.py to evaluate
multiple odometry CSV files against a single ground truth file.
"""

import os
import subprocess
import sys


def run_batch_evaluation():
    """
    Example function showing how to run the batch evaluation.

    Modify the paths below according to your setup:
    - gt_file: Path to your ground truth file
    - est_folder: Folder containing odometry_*.csv files
    - output_folder: Where to store the results
    """

    # Configuration - modify these paths
    gt_file = "path/to/your/ground_truth.csv"  # Ground truth file
    est_folder = "path/to/odometry/files"  # Folder with odometry_*.csv files
    output_folder = "path/to/output/results"  # Output folder for results

    # Command to run the batch evaluation
    cmd = [
        sys.executable,
        "odometry_evaluation_batch.py",
        "-gt",
        gt_file,
        "-est_folder",
        est_folder,
        "-out",
        output_folder,
        "--align",  # Enable alignment
        "--show_plots",  # Show plots in addition to saving
    ]

    print("Running batch evaluation with command:")
    print(" ".join(cmd))
    print()

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("Batch evaluation completed successfully!")
        print("Output:", result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"Error running batch evaluation: {e}")
        print("Error output:", e.stderr)
        return False

    return True


def print_usage_help():
    """Print help information about the batch evaluation script."""
    print("""
Batch Odometry Evaluation Script Usage:

The odometry_evaluation_batch.py script evaluates multiple odometry CSV files against a single ground truth file.

Basic Usage:
    python odometry_evaluation_batch.py -gt <ground_truth_file> -est_folder <input_folder> -out <output_folder>

Required Arguments:
    -gt, --gt_file          Path to ground truth GPS file (.csv or .txt)
    -est_folder, --est_folder  Path to folder containing estimated odometry CSV files
    -out, --output_folder   Path to output folder for results

Optional Arguments:
    -a, --align             Enable alignment with Umeyama's method
    -s, --correct_scale     Correct scale with Umeyama's method
    --align_origin          Align trajectory origin to reference trajectory origin
    -no_3d, --disable_plot_3d  Disable 3D plot generation
    -no_vel, --disable_plot_velocity  Disable velocity plots
    -show, --show_plots     Show plots in addition to saving them
    -pattern, --file_pattern  Pattern to match odometry files (default: "odometry_*.csv")

Example:
    python odometry_evaluation_batch.py \\
        -gt /path/to/ground_truth.csv \\
        -est_folder /path/to/odometry_files \\
        -out /path/to/results \\
        --align \\
        --show_plots

Output Structure:
    The script creates numbered subfolders (00, 01, 02, ...) in the output folder.
    Each subfolder contains:
    - ape_result.txt: APE evaluation results
    - trajectory_3d_comparison.png: 3D trajectory comparison plot
    - multiple_trajectories.png: Multiple trajectory visualization
    - translation_drift_per_sec_rpe.png: Translation drift plot
    - <original_odometry_file>.csv: Copy of the input odometry file
    """)


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print_usage_help()
    else:
        print("Example batch evaluation script")
        print("Run with --help to see usage information")
        print()

        # Uncomment the line below to run the example
        # run_batch_evaluation()
