import logging
import os

import evo.common_ape_rpe as common
import numpy as np
from evo import main_ape, main_rpe
from evo.core.trajectory import PoseTrajectory3D
from scipy.spatial.transform import Rotation
from utils import visualization


class APEMetric:
    """
    Class for computing Absolute Pose Error (APE) metrics.
    """

    def __init__(self, args, traj_ref: PoseTrajectory3D, traj_est: PoseTrajectory3D):
        """
        Initialize APEMetric object.

        Args:
            args: Arguments for APE computation.
            traj_ref: Reference trajectory.
            traj_est: Estimated trajectory.
        """
        super().__init__()
        self.logger = logging.getLogger(__name__ + ".APEMetric")
        self.args = args
        self.traj_ref = traj_ref
        self.traj_est = traj_est
        self.result = None

    def compute(
        self,
        align: bool | None = None,
        correct_scale: bool | None = None,
        n_to_align: int | None = None,
        align_origin: bool | None = None,
    ):
        """
        Compute Absolute Pose Error.

        Args:
            align (bool, optional): Whether to align the trajectories before computing APE. Defaults to None.
            correct_scale (bool, optional): Whether to correct the scale of the trajectories before computing APE.
                Defaults to None.
            n_to_align (int, optional): Number of poses to use for alignment. Defaults to None.
            align_origin (bool, optional): Whether to align the trajectories to the origin before computing APE.
                Defaults to None.

        Returns:
            Result of APE computation.

        Notes:
            This method computes the Absolute Pose Error (APE) between the reference and the estimated trajectories.
            It uses the evo.main_rpe.rpe function to perform the computation.
        """
        pose_relation = common.get_pose_relation(self.args)
        if align is None:
            align = self.args.align
        if correct_scale is None:
            correct_scale = self.args.correct_scale
        if n_to_align is None:
            n_to_align = self.args.n_to_align
        if align_origin is None:
            align_origin = self.args.align_origin

        self.result = main_ape.ape(
            self.traj_ref,
            self.traj_est,
            pose_relation=pose_relation,
            align=align,
            correct_scale=correct_scale,
            n_to_align=n_to_align,
            align_origin=align_origin,
        )

        self.logger.info("APE result %s", self.result)
        return self.result

    def plot_2d(
        self,
        topic_msgs: dict[str, np.ndarray],
        topic_dict: dict[str, str],
        output_folder: str,
        bag_filename: str,
        gt_traj_wgs84: PoseTrajectory3D,
        est_traj_wgs84: PoseTrajectory3D,
    ):
        """
        Generates and saves various 2D plots to visualize the Absolute Pose Error (APE) and
        trajectories of ground truth and estimated data in WGS84 and ENU coordinate systems.

        This method creates three types of plots:
        1. A 2D error plot showing the APE translation error over time.
        2. A plot of multiple trajectories, comparing the reference and estimated paths.
        3. A detailed plot showing the APE for both WGS84 and ENU coordinate systems,
        illustrating the error in latitude, longitude, and altitude (for WGS84) and
        the error in X, Y, and Z coordinates (for ENU).

        Args:
            topic_msgs (dict): A dictionary of topic messages.
            topic_dict (dict): Dictionary of topic names.
            output_folder (str): The output folder where the plot files will be saved.
            bag_filename (str): The name of the bag file.
            gt_traj_wgs84 (PoseTrajectory3D): The ground truth trajectory in WGS84 coordinates.
            est_traj_wgs84 (PoseTrajectory3D): The estimated trajectory in WGS84 coordinates.

        Returns:
            None

        Notes:
            This method plots the 2D Absolute Pose Error (APE) based on the computed APE result.
            If the APE result is not available, a warning message will be logged.
        """
        if self.result is not None:
            plot_2d_error_filename = os.path.join(output_folder, "ape_2d_error")
            visualization.plot_2d_error_array(
                result=self.result,
                timestamps=self.traj_ref.timestamps,
                topic_msgs=topic_msgs,
                topic_dict=topic_dict,
                error_type="APE (m)",
                title=f"APE translation error(m) for bag file: {bag_filename}",
                filename=plot_2d_error_filename,
                show_plot=self.args.show_plots,
            )
            plot_2d_trajectories_filename = os.path.join(output_folder, "ape_2d_projected_trajectories")
            visualization.multiple_trajectories(
                gt_traj=self.traj_ref,
                es_traj=self.traj_est,
                title=f"APE Projected Trajectory for bag file: {bag_filename}",
                filename=plot_2d_trajectories_filename,
                show_plot=self.args.show_plots,
            )
            plot_2d_xyz_error_filename = os.path.join(output_folder, "ape_wgs84_enu_error")
            visualization.plot_xyz_error(
                gt_traj_enu=self.traj_ref,
                est_traj_enu=self.traj_est,
                title=(
                    f"The APE for both coordinate systems "
                    f"WGS84 (left) and Cartesian Topcentric ENU (right): {bag_filename}"
                ),
                filename=plot_2d_xyz_error_filename,
                gt_traj_wgs84=gt_traj_wgs84,
                est_traj_wgs84=est_traj_wgs84,
                show_plot=self.args.show_plots,
            )
            plot_scale_filename = os.path.join(output_folder, "ape_scale")
            visualization.plot_scale(
                topic_msgs=topic_msgs,
                topic_dict=topic_dict,
                title=f"Estimated Scale for bag file: {bag_filename}",
                filename=plot_scale_filename,
                show_plot=self.args.show_plots,
            )
        else:
            self.logger.warning("No result to plot. Please run compute() first.")

    def plot_3d(
        self,
        bag_filename: str,
        topic_dict: dict[str, str],
        topic_msgs: dict[str, np.ndarray],
    ):
        """
        Absolute Pose Error 3D plot error with color map

        Args:
            bag_filename (str): The filename of the bag file.
            topic_dict (dict): A dictionary of topic names and their corresponding messages.
            topic_msgs (dict): A dictionary of topic messages.

        Returns:
            None

        Notes:
            This method plots the 3D error with a color map based on the computed APE result.
            If the RPE result is not available, a warning message will be logged.
            The plot will be saved as an HTML file in the specified output folder.
            The plot will show the ground truth trajectory, estimated trajectory, and the error between them.
            The color of the error points represents the magnitude of the error.
            The title of the plot will include the bag filename.
        """
        if self.result is not None:
            error = self.result.np_arrays["error_array"]
            filename = os.path.join(self.args.output_folder, "ape_3d")
            visualization.plot_3d(
                gt_traj=self.traj_ref,
                es_traj=self.traj_est,
                error=error,
                error_type="APE (m)",
                topic_msgs=topic_msgs,
                topic_dict=topic_dict,
                title=f"APE translation error(m) for bag file: {bag_filename}",
                filename=filename,
                show_plot=self.args.show_plots,
            )
        else:
            self.logger.warning("No result to plot. Please run compute() first.")

    def plot_3d_cov(
        self,
        bag_filename: str,
        topic_dict: dict[str, str],
        topic_msgs: dict[str, np.ndarray],
    ):
        """
        Plot 3D covariance matrix error with color map

        Args:
            bag_filename (str): The filename of the bag file.
            topic_dict (dict): A dictionary of topic names and their corresponding messages.
            topic_msgs (dict): A dictionary of topic messages.

        Returns:
            None

        Notes:
            This method plots the 3D covariance matrix error with a color map based on the translation part of computed
                covariance matrix.
            If the VIO or GPS translation covariance result is not available, a warning message will be logged.
            The plot will be saved as an HTML file in the specified output folder.
            The plot will show the ground truth trajectory, estimated trajectory, and the estimated covariance matrix
                between them.
            The point colors indicate the highest value in the translation component of the estimated covariance.
                Points colored black signify invalid or NaN (not a number) covariance values.
            The title of the plot will include the bag filename.
        """
        if "vio_pwc_topic" in topic_dict:
            filename = os.path.join(self.args.output_folder, "cov_3d")
            visualization.plot_3d_cov(
                cov_msgs=topic_msgs[topic_dict["vio_pwc_topic"]],
                title=f"VIO translation covariance for bag file: {bag_filename}",
                filename=filename,
                show_plot=self.args.show_plots,
            )

        if "sync_pwc_topic" in topic_dict:
            filename = os.path.join(self.args.output_folder, "cov_3d_gps")
            visualization.plot_3d_cov(
                cov_msgs=topic_msgs[topic_dict["sync_pwc_topic"]],
                title=f"GPS translation covariance for bag file: {bag_filename}",
                filename=filename,
                show_plot=self.args.show_plots,
            )


class RPEMetric:
    """
    Class for computing Relative Pose Error (RPE) metrics.
    """

    def __init__(self, args, traj_ref: PoseTrajectory3D, traj_est: PoseTrajectory3D):
        """
        Initialize RPEMetric object.

        Args:
            args: Arguments for RPE computation.
            traj_ref: Reference trajectory.
            traj_est: Estimated trajectory.
        """

        super().__init__()
        self.logger = logging.getLogger(__name__ + ".RPEMetric")
        self.args = args
        self.traj_ref = traj_ref
        self.traj_est = traj_est
        self.result = None

    def compute(
        self,
        align: bool | None = None,
        correct_scale: bool | None = None,
        n_to_align: int | None = None,
        align_origin: bool | None = None,
    ):
        """
        Compute Relative Pose Error.

        Args:
            align (bool, optional): Whether to align the trajectories before computing RPE. Defaults to None.
            correct_scale (bool, optional): Whether to correct the scale of the trajectories before computing RPE.
                Defaults to None.
            n_to_align (int, optional): Number of poses to use for alignment. Defaults to None.
            align_origin (bool, optional): Whether to align the trajectories to the origin before computing RPE.
                Defaults to None.

        Returns:
            Result of RPE computation.

        Notes:
            This method computes the Relative Pose Error (RPE) between the reference and the estimated trajectories.
            It uses the evo.main_rpe.rpe function to perform the computation.
        """
        delta_unit = common.get_delta_unit(self.args)
        pose_relation = common.get_pose_relation(self.args)
        if align is None:
            align = self.args.align
        if correct_scale is None:
            correct_scale = self.args.correct_scale
        if n_to_align is None:
            n_to_align = self.args.n_to_align
        if align_origin is None:
            align_origin = self.args.align_origin

        self.result = main_rpe.rpe(
            self.traj_ref,
            self.traj_est,
            pose_relation=pose_relation,
            delta=self.args.delta,
            delta_unit=delta_unit,
            align=align,
            correct_scale=correct_scale,
            n_to_align=n_to_align,
            align_origin=align_origin,
        )

        self.logger.info("GT information %s", self.traj_ref)
        self.logger.info("ES information %s", self.traj_est)

        self.logger.info("RPE result %s", self.result)
        return self.result

    def plot_2d(
        self,
        output_folder: str,
        bag_filename: str,
        topic_msgs: dict[str, np.ndarray],
        topic_dict: dict[str, str],
    ):
        """
        Generates and saves plots visualizing the Relative Pose Error (RPE) for a given dataset.

        This method creates two types of plots:
        1. A 2D plot illustrating the RPE translation error over time, which helps in understanding
        how the error evolves throughout the dataset.
        2. A plot showing the error in X, Y, and Z coordinates within the Cartesian Topcentric ENU
        coordinate system, providing a detailed view of the error distribution across different axes.

        Args:
            output_folder (str): The output folder where the plot will be saved.
            bag_filename (str): The filename of the bag file.
            topic_msgs (dict): A dictionary of topic messages.
            topic_dict (dict): Dictionary of topic names.

        Returns:
            None

        Notes:
            This method plots the 2D Relative Pose Error (RPE) based on the computed RPE result.
            If the RPE result is not available, a warning message will be logged.
            The plot will be saved as an HTML file in the specified output folder.
            The plot will show the translation error in meters for each timestamp in the reference trajectory.
            The title of the plot will include the bag filename.
        """
        if self.result is not None:
            plot_2d_error_filename = os.path.join(output_folder, "rpe_2d_error")
            visualization.plot_2d_error_array(
                result=self.result,
                timestamps=self.traj_ref.timestamps,
                topic_msgs=topic_msgs,
                topic_dict=topic_dict,
                error_type="RPE (m)",
                title=f"RPE translation error(m) for bag file: {bag_filename}",
                filename=plot_2d_error_filename,
                show_plot=self.args.show_plots,
            )

            plot_2d_xyz_error_filename = os.path.join(output_folder, "rpe_2d_xyz_error")
            visualization.plot_xyz_error(
                gt_traj_enu=self.traj_ref,
                est_traj_enu=self.traj_est,
                title=(f"The RPE for Cartesian Topcentric ENU coordinate system: {bag_filename}"),
                filename=plot_2d_xyz_error_filename,
                show_plot=self.args.show_plots,
            )

            if "altitude_topic" in topic_dict and topic_msgs[topic_dict["altitude_topic"]].size > 0:
                altitude_filename = os.path.join(output_folder, "altitude")
                visualization.plot_altitude(
                    topic_msgs=topic_msgs,
                    topic_dict=topic_dict,
                    title=(f"Altitude for bag file: {bag_filename}"),
                    filename=altitude_filename,
                    show_plot=self.args.show_plots,
                )

            if "processing_time_topic" in topic_dict and topic_msgs[topic_dict["processing_time_topic"]].size > 0:
                processing_time_filename = os.path.join(output_folder, "processing_time")
                visualization.plot_processing_time(
                    topic_msgs=topic_msgs,
                    topic_dict=topic_dict,
                    title=f"Processing time for bag file: {bag_filename}",
                    filename=processing_time_filename,
                    show_plot=self.args.show_plots,
                )

            if "velocity_topic" in topic_dict and topic_msgs[topic_dict["velocity_topic"]].size > 0:
                velocity_filename = os.path.join(output_folder, "velocity")
                visualization.plot_velocity(
                    topic_msgs=topic_msgs,
                    topic_dict=topic_dict,
                    title=f"Velocity for bag file: {bag_filename}",
                    filename=velocity_filename,
                    show_plot=self.args.show_plots,
                )

        else:
            self.logger.warning("No result to plot. Please run compute() first.")

    def plot_3d(
        self,
        bag_filename: str,
        topic_dict: dict[str, str],
        topic_msgs: dict[str, np.ndarray],
    ):
        """
        Relative Pose Error 3D plot error with color map

        Args:
            bag_filename (str): The filename of the bag file.
            topic_dict (dict): A dictionary of topic names and their corresponding messages.
            topic_msgs (dict): A dictionary of topic messages.

        Returns:
            None

        Notes:
            This method plots the 3D error with a color map based on the computed RPE result.
            If the RPE result is not available, a warning message will be logged.
            The plot will be saved as an HTML file in the specified output folder.
            The plot will show the ground truth trajectory, estimated trajectory, and the error between them.
            The color of the error points represents the magnitude of the error.
            The title of the plot will include the bag filename.
        """
        if self.result is not None:
            error = self.result.np_arrays["error_array"]
            filename = os.path.join(self.args.output_folder, "rpe_3d")
            visualization.plot_3d(
                gt_traj=self.traj_ref,
                es_traj=self.traj_est,
                error=error,
                error_type="RPE (m)",
                topic_msgs=topic_msgs,
                topic_dict=topic_dict,
                title=f"RPE translation error(m) for bag file: {bag_filename}",
                filename=filename,
                show_plot=self.args.show_plots,
            )
        else:
            self.logger.warning("No result to plot. Please run compute() first.")


class KittiMetric:
    """
    Class for computing KITTI-style trajectory evaluation metrics.

    This class computes translational and rotational errors for trajectory segments
    of varying lengths. The evaluation lengths are automatically determined based
    on the ground truth trajectory, typically ranging from 10% to 80% of the total
    trajectory length, divided into 8 segments.
    """

    def __init__(self, traj_ref: PoseTrajectory3D, traj_est: PoseTrajectory3D):
        """Initialize KittiMetric with reference and estimated trajectories.

        The class computes translational and rotational errors for all possible
        subsequences of varying lengths determined from the ground truth trajectory.

        Args:
            traj_ref (PoseTrajectory3D): Reference trajectory (ground truth)
            traj_est (PoseTrajectory3D): Estimated trajectory
        """
        super().__init__()
        self.logger = logging.getLogger(__name__ + ".KittiMetric")
        self.traj_ref = traj_ref
        self.traj_est = traj_est
        self.number_poses = traj_ref.num_poses

        # Calculate trajectory length and segment lengths
        self.lengths = np.array([100, 200, 300, 400, 500, 600, 700, 800])
        self.num_lengths = len(self.lengths)

    def trajectory_distances(self, trajectory: PoseTrajectory3D) -> list:
        """Compute cumulative distances for trajectory.

        Compute distance for each pose with respect to the first frame (frame-0).
        Each distance represents the cumulative path length from the start to that pose.

        Args:
            trajectory (PoseTrajectory3D): Input trajectory

        Returns:
            dist (list): List of cumulative distances from frame-0 to each pose
        """
        distances = [0]
        for i in range(1, self.number_poses):
            delta = trajectory.positions_xyz[i] - trajectory.positions_xyz[i - 1]
            distances.append(distances[-1] + np.linalg.norm(delta))
        return distances

    def last_frame_from_segment_length(self, dist: list, first_frame: int, length: float) -> int:
        """Find end frame index for given segment length.

        Find frame (index) that is away from the first_frame with the required distance.

        Args:
            dist (list): List of cumulative distances from frame-0
            first_frame (int): Start frame index
            length (float): Required segment length

        Returns:
            int: End frame index or -1 if not found
        """
        for i in range(first_frame, len(dist)):
            if dist[i] > dist[first_frame] + length:
                return i
        return -1

    def rotation_error(self, pose_error: np.ndarray) -> float:
        """Compute rotation error from pose error matrix.

        The rotation error is computed using the axis-angle representation:
        trace(R) = 1 + 2(cos(theta)) => theta = arccos(0.5*(trace(R)-1))

        References:
            http://www.boris-belousov.net/2016/12/01/quat-dist/
            https://en.wikipedia.org/wiki/Axis%E2%80%93angle_representation#Exponential_map_from_so.283.29_to_SO.283.29

        Args:
            pose_error (np.ndarray): 4x4 relative pose error matrix

        Returns:
            float: Rotation error in radians
        """
        d = 0.5 * (np.trace(pose_error[:3, :3]) - 1)
        return np.arccos(max(min(d, 1.0), -1.0))

    def translation_error(self, pose_error: np.ndarray) -> float:
        """Compute translation error from pose error matrix.

        Computes the Euclidean distance (norm 2) of the translation component
        of the pose error matrix.

        Args:
            pose_error (np.ndarray): 4x4 relative pose error matrix

        Returns:
            float: Translation error (Euclidean distance)
        """
        return float(np.linalg.norm(pose_error[:3, 3]))

    def calculate_sequence_error(self) -> list:
        """Calculate sequence errors for all evaluation lengths.

        For each evaluation length and each possible starting frame (sampled at regular
        intervals), computes the relative pose error between the ground truth and
        estimated trajectories.

        Returns:
            list: List of [first_frame, rotation error, translation error, length, speed] where:
                - first_frame: First frame index
                - rotation error: Rotation error per length
                - translation error: Translation error per length
                - length: Evaluation trajectory length
                - speed: Vehicle speed (computed from timestamps)
        """
        err = []
        dist = self.trajectory_distances(self.traj_ref)
        frame_step = 5

        for first_frame in range(0, self.number_poses, frame_step):
            for len_ in self.lengths:
                last_frame = self.last_frame_from_segment_length(dist, first_frame, len_)

                if last_frame == -1 or last_frame >= self.traj_est.num_poses:
                    continue

                pose_delta_gt = (
                    np.linalg.inv(self.traj_ref.poses_se3[first_frame]) @ self.traj_ref.poses_se3[last_frame]
                )

                pose_delta_result = (
                    np.linalg.inv(self.traj_est.poses_se3[first_frame]) @ self.traj_est.poses_se3[last_frame]
                )

                pose_error = np.linalg.inv(pose_delta_result) @ pose_delta_gt

                r_err = self.rotation_error(pose_error)
                t_err = self.translation_error(pose_error)
                dt = (self.traj_ref.timestamps[last_frame] - self.traj_ref.timestamps[first_frame]) * 1e-3
                speed = len_ / dt if dt > 0 else 0

                err.append([first_frame, r_err / len_, t_err / len_, len_, speed])

        return err

    def calculate_ave_errors(self, errors: list) -> tuple:
        """Calculate average rotation and translation errors for each length.

        For each evaluation length, computes the average rotation and translation
        errors across all segments of that length.

        Args:
            errors (list): List of sequence errors from calculate_sequence_error()

        Returns:
            tuple: (rotation errors in degrees, translation errors) where:
                - rotation errors: Average rotation error for each length
                - translation errors: Average translation error for each length
        """
        rotation_errors = []
        translation_errors = []

        for length in self.lengths:
            rotation_error_each_length = []
            translation_error_each_length = []

            for error in errors:
                if abs(error[3] - length) < 0.1:
                    rotation_error_each_length.append(error[1])
                    translation_error_each_length.append(error[2])

            if rotation_error_each_length:
                rotation_errors.append(np.mean(rotation_error_each_length))
                translation_errors.append(np.mean(translation_error_each_length))

        return np.array(rotation_errors) * 180 * 100 / np.pi, np.array(translation_errors) * 100

    def compute(self) -> tuple:
        """Compute average rotation and translation errors across all segments.

        This is the main evaluation function that computes the overall trajectory
        error metrics following the KITTI evaluation methodology.

        Returns:
            tuple: (mean rotation error in degrees, mean translation error) where:
                - mean rotation error: Average rotation error across all segments
                - mean translation error: Average translation error across all segments
        """
        errors = self.calculate_sequence_error()
        rotation_errors, translation_errors = self.calculate_ave_errors(errors)
        self.logger.info(f"Rotational error (deg/100m) {rotation_errors}")
        self.logger.info(f"Translational error (%): {translation_errors}")
        return rotation_errors, translation_errors


class TranslationalDriftPerSecondMetric:
    """
    Computes 3D translational drift per second by analyzing all components of trajectories with local rotation alignment

    This class calculates the drift between a reference trajectory (ground truth) and an estimated trajectory
    by computing the 3-norm of the translational error in the X, Y, and Z axes for each second. The metric applies
    local rotation alignment using scipy's align_vectors to reduce rotational drift between trajectories.
    """

    def __init__(self, traj_ref: PoseTrajectory3D, traj_est: PoseTrajectory3D, alignment_interval: float = 5.0):
        """
        Initializes TranslationalDriftPerSecondMetric with reference and estimated trajectories.

        Args:
            traj_ref (PoseTrajectory3D): Reference trajectory (ground truth).
            traj_est (PoseTrajectory3D): Estimated trajectory.
        Raises:
            ValueError: If the timestamps of the reference and estimated trajectories are not equal.
        """
        super().__init__()
        self.logger = logging.getLogger(__name__ + ".TranslationalDriftPerSecondMetric")
        self.traj_ref = traj_ref
        self.traj_est = traj_est

        if not np.array_equal(traj_ref.timestamps, traj_est.timestamps):
            raise ValueError("Reference and estimated trajectories must have identical timestamps")

        self.timestamps = traj_ref.timestamps
        self.number_poses = traj_ref.num_poses

        self.alignment_interval = alignment_interval
        self.current_alignment = None

    def get_second_intervals(self) -> list[tuple[int, int]]:
        """
        Determines the start and end indices for each one-second interval in the trajectories.

        This method iterates through the timestamps and identifies the indices that correspond to the
        beginning and end of each second.

        Returns:
            list[tuple[int, int]]: A list of tuples, where each tuple contains the (start_idx, end_idx)
                                    for a one-second interval.
        """
        intervals = []
        current_start_idx = 0
        current_second = self.timestamps[0]

        for i in range(1, self.number_poses):
            second = self.timestamps[i]
            if second - current_second > 1.0:
                intervals.append((current_start_idx, i))
                current_start_idx = i
                current_second = second

        # Add the last interval if there are remaining poses
        if current_start_idx < self.number_poses - 1:
            intervals.append((current_start_idx, self.number_poses - 1))

        return intervals

    def compute_rotation_alignment_from_positions(
        self, ref_positions: np.ndarray, est_positions: np.ndarray
    ) -> np.ndarray:
        """
        Compute rotation alignment matrix from reference and estimated position arrays.

        This function uses scipy's align_vectors to compute the optimal rotation transformation
        that aligns the estimated trajectory to the reference trajectory based on motion vectors.
        Only rotation is estimated, no translation component.

        Args:
            ref_positions (np.ndarray): Reference trajectory positions, shape (N, 3)
            est_positions (np.ndarray): Estimated trajectory positions, shape (N, 3)

        Returns:
            np.ndarray: 4x4 transformation matrix with rotation only (no translation)

        Raises:
            ValueError: If input arrays have different shapes or insufficient data points
        """
        if ref_positions.shape != est_positions.shape:
            raise ValueError(
                f"Reference and estimated position arrays must have the same shape. "
                f"Got {ref_positions.shape} and {est_positions.shape}"
            )

        if len(ref_positions) < 2:
            raise ValueError(f"Need at least 2 position points for alignment computation. Got {len(ref_positions)}")

        # Compute motion vectors for both trajectories
        ref_vectors = np.diff(ref_positions, axis=0)
        est_vectors = np.diff(est_positions, axis=0)

        # Filter out very small motion vectors (likely noise or stationary periods)
        valid_mask = (np.linalg.norm(ref_vectors, axis=1) > 1e-6) & (np.linalg.norm(est_vectors, axis=1) > 1e-6)

        if not np.any(valid_mask):
            return np.eye(4)

        ref_vectors = ref_vectors[valid_mask]
        est_vectors = est_vectors[valid_mask]

        rotation, rmsd = Rotation.align_vectors(ref_vectors, est_vectors)
        T = np.eye(4)
        T[:3, :3] = rotation.as_matrix()

        return T

    def compute_local_alignment(self, end_time: float) -> np.ndarray:
        """
        Compute local rotation alignment using the last window of trajectory data.

        This method uses scipy's align_vectors to compute the optimal rotation transformation
        that aligns the estimated trajectory to the reference trajectory over the specified time window.
        Only rotation is estimated, no translation component.

        Args:
            end_time (float): End timestamp for the alignment window

        Returns:
            np.ndarray: 4x4 transformation matrix with rotation only (no translation)
        """
        start_time = end_time - self.alignment_interval

        start_idx = None
        end_idx = None

        for i, timestamp in enumerate(self.timestamps):
            if start_idx is None and timestamp >= start_time:
                start_idx = i
            if timestamp <= end_time:
                end_idx = i

        if start_idx is None or end_idx is None or end_idx - start_idx < 2:
            self.logger.warning(f"Insufficient data for alignment computation at time {end_time}")
            return np.eye(4)

        ref_positions = self.traj_ref.positions_xyz[start_idx : end_idx + 1]
        est_positions = self.traj_est.positions_xyz[start_idx : end_idx + 1]

        try:
            return self.compute_rotation_alignment_from_positions(ref_positions, est_positions)
        except Exception as e:
            self.logger.error(f"Failed to compute alignment at time {end_time}: {e}")
            return np.eye(4)

    def apply_alignment_to_position(self, position: np.ndarray, alignment: np.ndarray) -> np.ndarray:
        """
        Apply rotation alignment transformation to a 3D position.

        Args:
            position (np.ndarray): 3D position vector [x, y, z]
            alignment (np.ndarray): 4x4 transformation matrix with rotation only

        Returns:
            np.ndarray: Transformed 3D position vector
        """
        R = alignment[:3, :3]
        transformed_position = R @ position

        return transformed_position

    def compute_interval_drift(
        self,
        start_idx: int,
        end_idx: int,
        alignment: np.ndarray,
    ) -> tuple[float, float]:
        """
        Compute 3D translational drift for a single interval with alignment correction.

        This method calculates the drift between reference and estimated trajectories
        for a given interval, applying the provided alignment transformation to reduce
        rotational drift effects.

        Args:
            start_idx (int): Start index of the interval
            end_idx (int): End index of the interval
            alignment (np.ndarray): 4x4 transformation matrix for alignment

        Returns:
            tuple[float, float]: A tuple containing:
                - drift_magnitude (float): 3D norm of the translational drift per second
                - current_time (float): Timestamp at the end of the interval
        """
        start_ref = self.traj_ref.positions_xyz[start_idx]
        end_ref = self.traj_ref.positions_xyz[end_idx]
        start_est = self.traj_est.positions_xyz[start_idx]
        end_est = self.traj_est.positions_xyz[end_idx]

        # Apply current alignment to estimated positions
        start_est_aligned = self.apply_alignment_to_position(start_est, alignment)
        end_est_aligned = self.apply_alignment_to_position(end_est, alignment)

        motion_ref = end_ref - start_ref
        motion_est_aligned = end_est_aligned - start_est_aligned

        dt = self.timestamps[end_idx] - self.timestamps[start_idx]

        x_drift = abs(motion_ref[0] - motion_est_aligned[0]) / dt
        y_drift = abs(motion_ref[1] - motion_est_aligned[1]) / dt
        z_drift = abs(motion_ref[2] - motion_est_aligned[2]) / dt

        drift_magnitude = np.linalg.norm([x_drift, y_drift, z_drift])

        return drift_magnitude

    def compute(self) -> tuple[np.ndarray, np.ndarray]:
        """
        Computes the 3D translational drift for each second interval with local alignment correction.

        This method calculates the drift in the X, Y, and Z axes between the reference and estimated trajectories
        for each one-second interval. Every n seconds, it computes a local SE3 alignment using the previous 5 seconds
        of trajectory data and applies this alignment to the estimated positions to reduce drift accumulation.
        The drift is normalized by the time difference to provide a drift-per-second metric.

        Returns:
            tuple[np.ndarray, np.ndarray]: A tuple containing:
                - xyz_drift (np.ndarray): Array of 3-norm of the drift (x, y, z) for each second interval.
                - intervals (np.ndarray): Array of time intervals in seconds.
        """
        intervals = self.get_second_intervals()

        results = {
            "timestamps": [],
            "xyz_drift": [],
        }

        # Track accumulated time for alignment updates
        accumulated_time = 0.0
        last_alignment_time = 0.0

        self.current_alignment = np.eye(4)

        self.logger.info(f"Starting drift computation with local rotation alignment every {self.alignment_interval}s")

        for interval_idx, (start_idx, end_idx) in enumerate(intervals):
            current_time = self.timestamps[end_idx]
            interval_duration = self.timestamps[end_idx] - self.timestamps[start_idx]
            accumulated_time += interval_duration

            if accumulated_time - last_alignment_time >= self.alignment_interval:
                if current_time >= self.alignment_interval:
                    self.current_alignment = self.compute_local_alignment(current_time)
                    last_alignment_time = accumulated_time
                else:
                    self.logger.debug(f"Skipping alignment computation - insufficient data at time {current_time:.2f}s")

            try:
                drift_magnitude = self.compute_interval_drift(start_idx, end_idx, self.current_alignment)
                results["xyz_drift"].append(drift_magnitude)
                results["timestamps"].append(current_time)
            except ValueError:
                continue

        results["xyz_drift"] = np.array(results["xyz_drift"])
        results["timestamps"] = np.array(results["timestamps"])

        # Log statistics
        if len(results["xyz_drift"]) > 0:
            self.logger.info(f"RMSE XYZ drift (m/s): {np.sqrt(np.mean(np.square(results['xyz_drift']))):.6f}")
            self.logger.info(f"Mean XYZ drift (m/s): {np.mean(results['xyz_drift']):.6f}")
            self.logger.info(f"STD XYZ drift (m/s): {np.std(results['xyz_drift']):.6f}")
            self.logger.info(f"Median XYZ drift (m/s): {np.median(results['xyz_drift']):.6f}")
            self.logger.info(f"Min XYZ drift (m/s): {np.min(results['xyz_drift']):.6f}")
            self.logger.info(f"Max XYZ drift (m/s): {np.max(results['xyz_drift']):.6f}")
        else:
            self.logger.warning("No valid drift measurements computed")

        return (results["xyz_drift"], results["timestamps"])
