import numpy as np


class KittiDepthMetric:
    def __init__(self):
        super().__init__()

    def compute(self, gt, pred):
        """
        Compute the kitti depth error metric
        Ref 1: https://www.cvlibs.net/datasets/kitti/eval_depth.php?benchmark=depth_prediction
        Ref 2: Sparsity Invariant CNNs
               https://www.cvlibs.net/publications/Uhrig2017THREEDV.pdf
        Ref 3: Depth Map Prediction from a Single Image using a Multi-Scale Deep Network
               https://proceedings.neurips.cc/paper_files/paper/2014/file/7bccfde7714a1ebadf06c5f4cea752c1-Paper.pdf
        """
        thresh = np.maximum((gt / pred), (pred / gt))

        # Maximal Mean Relative Error of δi = 1.25i for i ∈ {1, 2, 3}
        # Ref: Sparsity Invariant CNNs [https://www.cvlibs.net/publications/Uhrig2017THREEDV.pdf]
        a1 = (thresh < 1.25).mean()
        a2 = (thresh < 1.25**2).mean()
        a3 = (thresh < 1.25**3).mean()

        abs_rel = np.mean(np.abs(gt - pred) / gt)
        sq_rel = np.mean(((gt - pred) ** 2) / gt)

        rmse = (gt - pred) ** 2
        rmse = np.sqrt(rmse.mean())

        rmse_log = (np.log(gt) - np.log(pred)) ** 2
        rmse_log = np.sqrt(rmse_log.mean())

        err = np.log(pred) - np.log(gt)

        # Scale invariant logarithmic error [log(m)*100]
        # Ref: Depth Map Prediction from a Single Image using a Multi-Scale Deep Network, Eq. 3
        # [https://proceedings.neurips.cc/paper_files/paper/2014/file/7bccfde7714a1ebadf06c5f4cea752c1-Paper.pdf]
        silog = np.sqrt(np.mean(err**2) - np.mean(err) ** 2) * 100

        log_10 = (np.abs(np.log10(gt) - np.log10(pred))).mean()
        return dict(
            a1=a1,
            a2=a2,
            a3=a3,
            abs_rel=abs_rel,
            rmse=rmse,
            log_10=log_10,
            rmse_log=rmse_log,
            silog=silog,
            sq_rel=sq_rel,
        )
