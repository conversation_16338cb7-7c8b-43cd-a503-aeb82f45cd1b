#!/usr/bin/env python3
"""
Improved GPS Utilities
Helper functions for GPS data analysis with validation and filtering.
"""

from typing import Dict, Optional, Tuple

import numpy as np
import pandas as pd


def is_valid_gps_coordinate(lat: float, lon: float) -> bool:
    """
    Check if GPS coordinates are valid.

    Args:
        lat: Latitude
        lon: Longitude

    Returns:
        True if coordinates are valid, False otherwise
    """
    # Check for invalid values
    if pd.isna(lat) or pd.isna(lon):
        return False

    # Check for zero coordinates (common invalid value)
    if lat == 0 and lon == 0:
        return False

    # Check for out-of-range values
    if lat < -90 or lat > 90:
        return False

    if lon < -180 or lon > 180:
        return False

    return True


def filter_valid_gps_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Filter DataFrame to keep only valid GPS coordinates.

    Args:
        df: DataFrame with 'latitude' and 'longitude' columns

    Returns:
        DataFrame with only valid GPS coordinates
    """
    if "latitude" not in df.columns or "longitude" not in df.columns:
        raise ValueError("DataFrame must contain 'latitude' and 'longitude' columns")

    # Create mask for valid coordinates
    valid_mask = df.apply(lambda row: is_valid_gps_coordinate(row["latitude"], row["longitude"]), axis=1)

    filtered_df = df[valid_mask].copy()

    if len(filtered_df) == 0:
        raise ValueError("No valid GPS coordinates found in the data")

    return filtered_df


def find_gps_bounds_robust(df: pd.DataFrame) -> Dict:
    """
    Find the most southwest and northeast positions from GPS DataFrame with validation.

    Args:
        df: DataFrame with 'latitude' and 'longitude' columns

    Returns:
        Dictionary with southwest and northeast coordinates
    """
    # Filter out invalid coordinates
    valid_df = filter_valid_gps_data(df)

    min_lat = valid_df["latitude"].min()
    max_lat = valid_df["latitude"].max()
    min_lon = valid_df["longitude"].min()
    max_lon = valid_df["longitude"].max()

    return {
        "southwest": {"latitude": min_lat, "longitude": min_lon},
        "northeast": {"latitude": max_lat, "longitude": max_lon},
        "bounds": {"min_lat": min_lat, "max_lat": max_lat, "min_lon": min_lon, "max_lon": max_lon},
        "total_valid_points": len(valid_df),
        "total_original_points": len(df),
        "invalid_points_removed": len(df) - len(valid_df),
    }


def analyze_gps_file_robust(csv_path: str) -> Dict:
    """
    Analyze a GPS CSV file with robust validation.

    Args:
        csv_path: Path to the CSV file

    Returns:
        Dictionary with complete analysis results
    """
    df = pd.read_csv(csv_path)

    # Get robust bounds
    bounds = find_gps_bounds_robust(df)

    # Calculate extent
    lat_span_deg = bounds["bounds"]["max_lat"] - bounds["bounds"]["min_lat"]
    lon_span_deg = bounds["bounds"]["max_lon"] - bounds["bounds"]["min_lon"]

    # Convert to kilometers
    avg_lat = (bounds["bounds"]["min_lat"] + bounds["bounds"]["max_lat"]) / 2
    lat_span_km = lat_span_deg * 111
    lon_span_km = lon_span_deg * 111 * np.cos(np.radians(avg_lat))

    extent = {
        "lat_span_km": lat_span_km,
        "lon_span_km": lon_span_km,
        "lat_span_deg": lat_span_deg,
        "lon_span_deg": lon_span_deg,
    }

    return {
        "file_path": csv_path,
        "bounds": bounds,
        "extent": extent,
        "data_quality": {
            "total_points": bounds["total_original_points"],
            "valid_points": bounds["total_valid_points"],
            "invalid_points": bounds["invalid_points_removed"],
            "valid_percentage": (bounds["total_valid_points"] / bounds["total_original_points"]) * 100,
        },
    }


def print_analysis_results(analysis: Dict):
    """Print analysis results in a formatted way."""
    print(f"\n=== GPS Analysis for {analysis['file_path']} ===")

    # Data quality info
    quality = analysis["data_quality"]
    print(f"Data Quality:")
    print(f"  Total points: {quality['total_points']:,}")
    print(f"  Valid points: {quality['valid_points']:,}")
    print(f"  Invalid points removed: {quality['invalid_points']:,}")
    print(f"  Valid percentage: {quality['valid_percentage']:.1f}%")

    # Bounds info
    bounds = analysis["bounds"]
    print(f"\n📍 Extreme Positions:")
    print(f"  Southwest: {bounds['southwest']['latitude']:.8f}°, {bounds['southwest']['longitude']:.8f}°")
    print(f"  Northeast: {bounds['northeast']['latitude']:.8f}°, {bounds['northeast']['longitude']:.8f}°")

    # Extent info
    extent = analysis["extent"]
    print(f"\n📐 Trajectory Extent:")
    print(f"  Latitude span: {extent['lat_span_deg']:.6f}° ({extent['lat_span_km']:.2f} km)")
    print(f"  Longitude span: {extent['lon_span_deg']:.6f}° ({extent['lon_span_km']:.2f} km)")
    print(f"  Total area: {extent['lat_span_km'] * extent['lon_span_km']:.2f} km²")


# Example usage
if __name__ == "__main__":
    files = ["input/gilching_300.csv", "input/kitu_07_30.csv", "input/new_quantum_switch_ssd.csv"]

    for file_path in files:
        try:
            analysis = analyze_gps_file_robust(file_path)
            print_analysis_results(analysis)
        except Exception as e:
            print(f"\nError processing {file_path}: {e}")
