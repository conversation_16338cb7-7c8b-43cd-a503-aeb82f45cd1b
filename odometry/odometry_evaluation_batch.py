import argparse
import glob
import logging.config
import os
import shutil
from pathlib import Path

from metric.pose_metric import APEMetric, TranslationalDriftPerSecondMetric
from utils import point3d_ops
from utils.data_loader import DataLoader
from utils.visualization import multiple_trajectories, plot_3d, plot_translation_drift_per_seconds


def parser():
    # create the top-level parser
    parsers = argparse.ArgumentParser(description="Batch CSV Evaluation with Velocity")
    input_opts = parsers.add_argument_group("input")
    input_opts.add_argument("-gt", "--gt_file", type=str, required=True, help="Path to ground truth gps file")
    input_opts.add_argument(
        "-est_folder",
        "--est_folder",
        type=str,
        required=True,
        help="Path to folder containing estimated odometry CSV files",
    )
    input_opts.add_argument(
        "-out", "--output_folder", type=str, required=True, help="Path to output folder for results"
    )

    algo_opts = parsers.add_argument_group("algorithm options")
    algo_opts.add_argument(
        "-r",
        "--pose_relation",
        default="trans_part",
        help="pose relation on which the APE is based",
        choices=[
            "full",
            "trans_part",
            "rot_part",
            "angle_deg",
            "angle_rad",
            "point_distance",
            "point_distance_error_ratio",
        ],
    )
    algo_opts.add_argument("-a", "--align", help="alignment with Umeyama's method", action="store_true")
    algo_opts.add_argument(
        "-s",
        "--correct_scale",
        action="store_true",
        help="correct scale with Umeyama's method",
    )
    algo_opts.add_argument(
        "--n_to_align",
        help="the number of poses to use for Umeyama alignment, counted from the start (default: all)",
        default=-1,
        type=int,
    )
    algo_opts.add_argument(
        "--align_origin",
        help="align the trajectory origin to the origin of the reference trajectory",
        action="store_true",
    )
    algo_opts.add_argument(
        "-d",
        "--delta",
        type=float,
        default=1,
        help="""delta parameter represents the distance interval used to match poses for relative pose estimation."
        "It defines the spatial gap between consecutive poses that should be considered when calculating the relative"
        "transformation or movement from one pose to another""",
    )
    algo_opts.add_argument(
        "-t",
        "--delta_tol",
        type=float,
        default=0.1,
        help="relative delta tolerance for all_pairs mode",
    )
    algo_opts.add_argument(
        "-u",
        "--delta_unit",
        default="f",
        help="unit of delta - `f` (frames), `d` (deg), `r` (rad), `m`(meters)",
        choices=["f", "d", "r", "m"],
    )

    output_opts = parsers.add_argument_group("output options")
    output_opts.add_argument("-no_3d", "--disable_plot_3d", help="disable draw 3D plot", action="store_true")
    output_opts.add_argument("-no_vel", "--disable_plot_velocity", help="disable velocity plots", action="store_true")
    output_opts.add_argument("-show", "--show_plots", help="show plots in addition to saving them", action="store_true")
    output_opts.add_argument(
        "-pattern", "--file_pattern", default="odometry_*.csv", help="Pattern to match odometry files"
    )

    args, _ = parsers.parse_known_args()
    return args


def load_logger():
    current_folder = os.path.dirname(os.path.abspath(__file__))
    file_location = os.path.join(current_folder, "logging.conf")
    logging.config.fileConfig(file_location)


def load_ground_truth(args, logger):
    """Load ground truth trajectory once."""
    data_loader = DataLoader()
    gt_ext = os.path.splitext(args.gt_file)[1].lower()

    if gt_ext == ".csv":
        logger.info("Loading ground truth trajectory from CSV...")
        gt_trajectory_data = data_loader.load_tum_csv(args.gt_file, include_linear_velocity=False)
    elif gt_ext == ".txt":
        logger.info("Loading ground truth trajectory from TXT...")
        gt_trajectory_data = data_loader.load_tum_txt(args.gt_file, include_linear_velocity=False)
    else:
        raise ValueError(f"Unsupported ground truth file extension: {gt_ext}. Supported extensions: .csv, .txt")

    logger.info("Ground truth trajectory loaded successfully")
    return gt_trajectory_data


def load_estimated_trajectory(est_file, logger):
    """Load estimated trajectory from file."""
    data_loader = DataLoader()
    est_ext = os.path.splitext(est_file)[1].lower()

    if est_ext == ".csv":
        logger.info(f"Loading estimated trajectory from CSV: {est_file}")
        est_trajectory_data = data_loader.load_tum_csv(est_file, include_linear_velocity=False)
    elif est_ext == ".txt":
        logger.info(f"Loading estimated trajectory from TXT: {est_file}")
        est_trajectory_data = data_loader.load_tum_txt(est_file, include_linear_velocity=False)
    else:
        raise ValueError(
            f"Unsupported estimated trajectory file extension: {est_ext}. Supported extensions: .csv, .txt"
        )

    return est_trajectory_data


def process_single_file(args, gt_trajectory_data, est_file, output_subfolder, logger):
    """Process a single estimated trajectory file."""
    try:
        # Load estimated trajectory
        est_trajectory_data = load_estimated_trajectory(est_file, logger)

        # Synchronize trajectories
        gt_traj_local, est_traj_local = point3d_ops.sync_gt_with_estimated_vo_msgs(
            gt_wgs84_msg_np=gt_trajectory_data, est_msg_np=est_trajectory_data, coordinate_system="ned"
        )

        logger.info(f"GT trajectory information: {gt_traj_local}")
        logger.info(f"Estimated trajectory information: {est_traj_local}")

        # Compute Absolute Pose Error (APE)
        ape_metric = APEMetric(args, gt_traj_local, est_traj_local)
        ape_result = ape_metric.compute(
            align=args.align,
            correct_scale=args.correct_scale,
            n_to_align=args.n_to_align,
            align_origin=args.align_origin,
        )

        # Plot 3D trajectories with error
        if not args.disable_plot_3d:
            logger.info("Generating 3D trajectory plots...")
            out_3d_filename = os.path.join(output_subfolder, "trajectory_3d_comparison")
            plot_3d(
                gt_traj=gt_traj_local,
                es_traj=est_traj_local,
                error=ape_result.np_arrays["error_array"],
                error_type="APE (m)",
                title="3D Trajectory Comparison with APE Error",
                filename=out_3d_filename,
                show_plot=args.show_plots,
            )

            out_3d_filename = os.path.join(output_subfolder, "multiple_trajectories")
            multiple_trajectories(
                gt_traj=gt_traj_local,
                es_traj=est_traj_local,
                title="3D Trajectory Comparison with APE Error",
                filename=out_3d_filename,
                show_plot=args.show_plots,
            )

        # Compute Translation Drift Per Second
        trans_drift_metric = TranslationalDriftPerSecondMetric(
            ape_metric.traj_ref, ape_metric.traj_est, alignment_interval=5.0
        )
        translation_drift_per_sec, translation_drift_timestamp = trans_drift_metric.compute()

        out_translation_drift_filename = os.path.join(output_subfolder, "translation_drift_per_sec_rpe")
        plot_translation_drift_per_seconds(
            translation_errors=translation_drift_per_sec,
            timestamps=translation_drift_timestamp,
            title="Translation Drift Per Seconds (m/s)",
            filename=out_translation_drift_filename,
            show_plot=args.show_plots,
        )

        # Save APE results
        result_text_filename = os.path.join(output_subfolder, "ape_result.txt")
        with open(result_text_filename, "w", encoding="utf-8") as f:
            f.write(str(ape_result))

        # Copy input odometry file
        input_copy_filename = os.path.join(output_subfolder, os.path.basename(est_file))
        shutil.copy2(est_file, input_copy_filename)

        logger.info(f"Successfully processed: {est_file}")
        return True

    except Exception as e:
        logger.error(f"Error processing {est_file}: {str(e)}")
        return False


def main():
    load_logger()
    logger = logging.getLogger("csv_eval")
    args = parser()

    # Create output folder if it doesn't exist
    if not os.path.exists(args.output_folder):
        os.makedirs(args.output_folder)

    # Load ground truth once
    gt_trajectory_data = load_ground_truth(args, logger)

    # Find all odometry files in the input folder
    pattern = os.path.join(args.est_folder, args.file_pattern)
    odometry_files = glob.glob(pattern)

    if not odometry_files:
        logger.error(f"No files found matching pattern: {pattern}")
        return

    logger.info(f"Found {len(odometry_files)} odometry files to process")

    # Process each file
    successful_count = 0
    for i, est_file in enumerate(sorted(odometry_files)):
        # Create subfolder with zero-padded number
        subfolder_name = f"{i:02d}"
        output_subfolder = os.path.join(args.output_folder, subfolder_name)

        if not os.path.exists(output_subfolder):
            os.makedirs(output_subfolder)

        logger.info(f"Processing file {i + 1}/{len(odometry_files)}: {est_file}")

        if process_single_file(args, gt_trajectory_data, est_file, output_subfolder, logger):
            successful_count += 1

    logger.info(f"Batch processing completed. Successfully processed {successful_count}/{len(odometry_files)} files.")


if __name__ == "__main__":
    main()
