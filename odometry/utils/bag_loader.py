import logging
import os
from collections import defaultdict

import numpy as np
import yaml
from rclpy.serialization import deserialize_message
from rosbag2_py import ConverterOptions, SequentialReader, StorageOptions
from rosidl_runtime_py.utilities import get_message


class BagLoader:
    def __init__(self):
        """
        Load the ground-truth and estimated trajectories from the csv file
        """
        self.logger = logging.getLogger(__name__)

    def parse_topic(self, msg, msg_type: str):
        """
        Parse the given message based on its type.

        Parameters:
            msg: The message to be parsed.
            msg_type (str): The type of the message.

        Returns:
            A list containing the parsed values based on the message type.

        Raises:
            None.

        Example usage:
            msg = ...
            msg_type = "sensor_msgs/msg/NavSatFix"
            parsed_values = parse_topic(msg, msg_type)
        """

        if msg_type == "sensor_msgs/msg/NavSatFix":
            return [
                msg.header.stamp.sec * 1e3 + msg.header.stamp.nanosec * 1e-6,
                msg.latitude,
                msg.longitude,
                msg.altitude,
            ]
        elif msg_type == "nav_msgs/msg/Odometry":
            return [
                msg.header.stamp.sec * 1e3 + msg.header.stamp.nanosec * 1e-6,
                msg.pose.pose.position.x,
                msg.pose.pose.position.y,
                msg.pose.pose.position.z,
                msg.pose.pose.orientation.w,
                msg.pose.pose.orientation.x,
                msg.pose.pose.orientation.y,
                msg.pose.pose.orientation.z,
            ]
        elif msg_type == "nav_msgs/msg/Path":
            return [
                msg.header.stamp.sec * 1e3 + msg.header.stamp.nanosec * 1e-6,
                msg.poses[-1].pose.position.x,
                msg.poses[-1].pose.position.y,
                msg.poses[-1].pose.position.z,
                msg.poses[-1].pose.orientation.w,
                msg.poses[-1].pose.orientation.x,
                msg.poses[-1].pose.orientation.y,
                msg.poses[-1].pose.orientation.z,
            ]
        elif msg_type == "geometry_msgs/msg/PoseWithCovarianceStamped":
            return [
                msg.header.stamp.sec * 1e3 + msg.header.stamp.nanosec * 1e-6,
                msg.pose.pose.position.x,
                msg.pose.pose.position.y,
                msg.pose.pose.position.z,
                msg.pose.pose.orientation.w,
                msg.pose.pose.orientation.x,
                msg.pose.pose.orientation.y,
                msg.pose.pose.orientation.z,
            ] + msg.pose.covariance.tolist()

        elif msg_type == "geometry_msgs/msg/PoseStamped":
            return [
                msg.header.stamp.sec * 1e3 + msg.header.stamp.nanosec * 1e-6,
                msg.pose.position.x,
                msg.pose.position.y,
                msg.pose.position.z,
                msg.pose.orientation.w,
                msg.pose.orientation.x,
                msg.pose.orientation.y,
                msg.pose.orientation.z,
            ]

        elif msg_type == "geometry_msgs/msg/PointStamped":
            return [
                msg.header.stamp.sec * 1e3 + msg.header.stamp.nanosec * 1e-6,
                msg.point.x,
                msg.point.y,
                msg.point.z,
            ]

        elif msg_type == "geometry_msgs/msg/TwistStamped":
            return [
                msg.header.stamp.sec * 1e3 + msg.header.stamp.nanosec * 1e-6,
                msg.twist.linear.x,
                msg.twist.linear.y,
                msg.twist.linear.z,
                msg.twist.angular.x,
                msg.twist.angular.y,
                msg.twist.angular.z,
            ]

        else:
            self.logger.error("Your message type is not yet supported.")

    def read_config_file(self, config_file: str) -> tuple[dict[str, str], set[str]]:
        """
        Read topic names from a yaml config file.

        Args:
            :param config_file: Yaml file with topic names

        Returns:
            :return: list of topic names
        """
        if not os.path.isfile(config_file):
            raise ValueError(f"Config file path: {config_file} is not a file.")

        self.logger.info("Reading topic names from config file: %s", config_file)
        with open(config_file, encoding="utf-8") as file:
            topic_dict = yaml.safe_load(file)["evaluation_topics"]

        def traverse_and_add(data, values):
            if isinstance(data, str):
                values.add(data)
            elif isinstance(data, dict):
                for _, value in data.items():
                    traverse_and_add(value, values)
            elif isinstance(data, list):
                for item in data:
                    traverse_and_add(item, values)

        traverse_and_add(topic_dict, topic_names := set())

        return topic_dict, topic_names

    def read_topics_bag(
        self, bag_file: str, topic_names: set[str]
    ) -> tuple[dict[str, np.ndarray], defaultdict[str, list]]:
        """
        Read the topics from a bag file and load them into a dictionary.
        """
        if not os.path.isdir(bag_file):
            raise ValueError(f"Bag file path: {bag_file} is not a directory.")

        topic_msgs = {topic: [] for topic in topic_names}
        topic_timestamps = defaultdict(list)
        self.logger.info("Reading topics from bag: %s", bag_file)

        storage_options = StorageOptions(uri=bag_file)
        converter_options = ConverterOptions(input_serialization_format="cdr", output_serialization_format="cdr")
        reader = SequentialReader()
        reader.open(storage_options, converter_options)

        topic_types = reader.get_all_topics_and_types()
        type_map = {topic_types[i].name: topic_types[i].type for i in range(len(topic_types))}

        while reader.has_next():
            (topic, data, timestamp) = reader.read_next()
            if topic in topic_names:
                msg_type = type_map[topic]
                if msg_type != "nav_msgs/msg/Path":
                    msg = deserialize_message(data, get_message(msg_type))
                    topic_msgs[topic].append(self.parse_topic(msg, msg_type))
                topic_timestamps[topic].append(timestamp)

        for key in topic_msgs:
            topic_msgs[key] = np.array(topic_msgs[key])

        return topic_msgs, topic_timestamps

    def print_topic_statistics(self, bag_filename: str, topic_msgs: dict[str, np.ndarray]):
        """
        Print the statistics of the bag file.

        Args:
            bag_filename: The name of the bag file.
            topic_msgs: The messages of the topics.

        Returns:
            None.
        """
        self.logger.info(f"Statistics of the bag file: {bag_filename}")
        for topic, msgs in topic_msgs.items():
            self.logger.info(f"Topic: {topic}, Number of messages: {msgs.shape[0]}")
