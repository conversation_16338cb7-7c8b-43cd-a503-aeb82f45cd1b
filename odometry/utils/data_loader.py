import logging
import re
from datetime import datetime

import numpy as np
import pandas as pd
import srt
from evo.core.trajectory import PosePath3D

from . import transformation


class DataLoader:
    def __init__(self):
        """
        Load the ground-truth and estimated trajectories from the csv file
        """
        self.logger = logging.getLogger(__name__)

    def parse_topic(self, msg, msg_type):
        """
        Parse the given message based on its type.

        Parameters:
            msg: The message to be parsed.
            msg_type: The type of the message.

        Returns:
            A list containing the parsed values based on the message type.

        Raises:
            None.

        Example usage:
            msg = ...
            msg_type = "sensor_msgs/msg/NavSatFix"
            parsed_values = parse_topic(msg, msg_type)
        """

        if msg_type == "sensor_msgs/msg/NavSatFix":
            return [
                msg.header.stamp.sec * 1e3 + msg.header.stamp.nanosec * 1e-6,
                msg.latitude,
                msg.longitude,
                msg.altitude,
            ]
        elif msg_type == "nav_msgs/msg/Odometry":
            return [
                msg.header.stamp.sec * 1e3 + msg.header.stamp.nanosec * 1e-6,
                msg.pose.pose.position.x,
                msg.pose.pose.position.y,
                msg.pose.pose.position.z,
                msg.pose.pose.orientation.w,
                msg.pose.pose.orientation.x,
                msg.pose.pose.orientation.y,
                msg.pose.pose.orientation.z,
            ]
        elif msg_type == "nav_msgs/msg/Path":
            return [
                msg.header.stamp.sec * 1e3 + msg.header.stamp.nanosec * 1e-6,
                msg.poses[-1].pose.position.x,
                msg.poses[-1].pose.position.y,
                msg.poses[-1].pose.position.z,
                msg.poses[-1].pose.orientation.w,
                msg.poses[-1].pose.orientation.x,
                msg.poses[-1].pose.orientation.y,
                msg.poses[-1].pose.orientation.z,
            ]
        elif msg_type == "geometry_msgs/msg/PoseWithCovarianceStamped":
            return [
                msg.header.stamp.sec * 1e3 + msg.header.stamp.nanosec * 1e-6,
                msg.pose.pose.position.x,
                msg.pose.pose.position.y,
                msg.pose.pose.position.z,
                msg.pose.pose.orientation.w,
                msg.pose.pose.orientation.x,
                msg.pose.pose.orientation.y,
                msg.pose.pose.orientation.z,
            ] + msg.pose.covariance.tolist()

        else:
            self.logger.error("Your message type is not yet supported.")

    def load_xlsm_tracking_data(self, file_path: str, gt_msg_np: np.ndarray) -> np.ndarray:
        """
        Load tracking data from an XLSM file and convert it to a numpy array.

        Args:
            file_path (str): Path to the XLSM file containing tracking data.
            gt_msg_np (np.ndarray): Ground truth trajectory data as a numpy array.

        Returns:
            np.ndarray: A numpy array of loaded tracking data with timestamps adjusted to match the ground truth.
        """
        # Initialize lists to store parsed data
        times, latitudes, longitudes, altitudes, timestamps = [], [], [], [], []

        # Read the Excel file, skipping the first 14 rows
        df = pd.read_excel(file_path, engine="openpyxl", skiprows=14)
        data = df.iloc[:, [0, 5, 6, 7]].values

        # Get the first timestamp from gt_traj_wgs84
        gt_first_timestamp = gt_msg_np[0, 0]

        # Iterate through each row of the data
        for row in data:
            # Extract and store time and position data
            times.append(row[0])
            latitudes.append(float(row[1]))
            longitudes.append(float(row[2]))
            altitudes.append(float(row[3]))

            # Convert time string to timestamp
            time_str = row[0]
            full_datetime = datetime(
                2024,
                5,
                8,
                int(time_str[:2]),
                int(time_str[3:5]),
                int(time_str[6:8]),
                int(time_str[9:]),
            )
            timestamp_ms = int(full_datetime.timestamp() * 1000)
            timestamps.append(timestamp_ms)

        # Calculate the offset
        csv_first_timestamp = timestamps[0]
        offset = csv_first_timestamp - gt_first_timestamp

        # Apply the offset to gt_msg_np timestamps
        gt_msg_np[:, 0] += offset

        self.logger.info(f"Applied offset of {offset} ms to ground truth timestamps")

        # Log the number of loaded poses
        self.logger.info("Loaded %d tracking poses from: %s", len(timestamps), file_path)

        # Create and return a simple numpy array
        return np.column_stack(
            (
                np.array(timestamps),
                np.array(latitudes),
                np.array(longitudes),
                np.array(altitudes),
            )
        )

    def load_tum_csv(self, csv_file: str, include_linear_velocity: bool = False) -> np.ndarray:
        """
        Load a trajectory in TUM format from a CSV file.

        The CSV file is expected to have the following columns:
        - Timestamps in nanoseconds (ns) which will be converted to milliseconds (ms)
        - tx, ty, tz (translation coordinates)
        - qx, qy, qz, qw (quaternion orientation) - will be reordered to wxyz format
        - vx, vy, vz (linear velocity coordinates) - optional

        The first two rows of the CSV file are ignored as they contain explanations.

        Parameters:
            csv_file (str): Path to the CSV file.
            include_linear_velocity (bool): Whether to load linear velocity data. Default is False.

        Returns:
            np.ndarray: The loaded trajectory in an array with size 8*n (pose only) or 14*n (pose + velocity).
        """
        df = pd.read_csv(csv_file, sep=",", header=None, skiprows=2)
        data = []

        for _, row in df.iterrows():
            # Check if we have all 14 parameters
            if len(row) < 14:
                continue

            timestamp_ms = row[0]
            pos_xyz = row[1:4].astype(float)
            quat_xyzw = row[4:8].astype(float)

            # Reorder quaternion from xyzw to wxyz
            quat_wxyz = np.roll(quat_xyzw, 1)

            # Basic pose data
            row_data = [timestamp_ms] + pos_xyz.tolist() + quat_wxyz.tolist()

            # Add velocity data if requested and available
            if include_linear_velocity:
                if len(row) >= 11:  # Check if velocity data is available
                    linear_vel = row[8:11].astype(float)  # vx, vy, vz
                    row_data.extend(linear_vel.tolist())
                else:
                    self.logger.warning("Linear velocity data requested but not available in CSV file")
                    row_data.extend([0.0, 0.0, 0.0])

            data.append(row_data)

        if include_linear_velocity:
            self.logger.info("Loaded %d poses with velocity from: %s", len(data), csv_file)
        else:
            self.logger.info("Loaded %d poses from: %s", len(data), csv_file)
        return np.array(data)

    def load_gps_csv(self, csv_file: str) -> np.ndarray:
        """
        Load GPS data from a CSV file.

        The CSV file is expected to have the following columns:
        - Timestamp (in seconds)
        - Latitude (decimal degrees)
        - Longitude (decimal degrees)
        - Altitude (meters)

        The first two rows of the CSV file are ignored as they contain explanations.

        Parameters:
            csv_file (str): Path to the CSV file.

        Returns:
            np.ndarray: The loaded GPS data in an array with size 4*n (timestamp, lat, lon, alt).
        """
        df = pd.read_csv(csv_file, sep=",", header=None, skiprows=2)
        data = []

        for _, row in df.iterrows():
            timestamp = row[0]

            lat = float(row[1])
            lon = float(row[2])
            alt = float(row[3])

            data.append([timestamp, lat, lon, alt])

        self.logger.info("Loaded %d GPS points from: %s", len(data), csv_file)
        return np.array(data)

    def load_gps_txt(self, txt_file: str) -> np.ndarray:
        """
        Load GPS data from a TXT file.

        The TXT file is expected to have the following columns (space-separated):
        - Timestamp (in seconds)
        - Latitude (decimal degrees)
        - Longitude (decimal degrees)
        - Altitude (meters)

        Parameters:
            txt_file (str): Path to the TXT file.

        Returns:
            np.ndarray: The loaded GPS data in an array with size 4*n (timestamp, lat, lon, alt).
        """
        data = []

        with open(txt_file, encoding="utf-8") as f:
            for line in f:
                # Skip empty lines
                line = line.strip()
                if not line:
                    continue

                nums = [float(n) for n in line.split()]

                # Expect at least 4 values: timestamp, lat, lon, alt
                if len(nums) >= 4:
                    timestamp = nums[0]
                    lat = nums[1]
                    lon = nums[2]
                    alt = nums[3]

                    data.append([timestamp, lat, lon, alt])

        self.logger.info("Loaded %d GPS points from: %s", len(data), txt_file)
        return np.array(data)

    def load_tum_txt(self, txt_file: str, include_linear_velocity: bool = False) -> np.ndarray:
        """
        Load a trajectory in TUM format from a TXT file.

        The TXT file is expected to have the following columns (space-separated):
        - Timestamp (in seconds)
        - tx, ty, tz (translation coordinates)
        - qx, qy, qz, qw (quaternion orientation) - will be reordered to wxyz format
        - vx, vy, vz (linear velocity coordinates) - optional

        Parameters:
            txt_file (str): Path to the TXT file.
            include_linear_velocity (bool): Whether to load linear velocity data. Default is False.

        Returns:
            np.ndarray: The loaded trajectory in an array with size 8*n (pose only) or 11*n (pose + velocity).
        """
        data = []

        with open(txt_file, encoding="utf-8") as f:
            for line in f:
                nums = [float(n) for n in line.rstrip().split(" ")]

                # Check if we have at least 8 parameters (timestamp + 3 pos + 4 quat)
                if len(nums) < 8:
                    continue

                timestamp = nums[0]
                pos_xyz = nums[1:4]
                quat_xyzw = nums[4:8]

                # Reorder quaternion from xyzw to wxyz
                quat_wxyz = np.roll(np.array(quat_xyzw), 1)

                # Basic pose data
                row_data = [timestamp] + pos_xyz + quat_wxyz.tolist()

                # Add velocity data if requested and available
                if include_linear_velocity:
                    if len(nums) >= 11:  # Check if velocity data is available
                        linear_vel = nums[8:11]  # vx, vy, vz
                        row_data.extend(linear_vel)
                    else:
                        self.logger.warning("Linear velocity data requested but not available in TXT file")
                        row_data.extend([0.0, 0.0, 0.0])

                data.append(row_data)

        if include_linear_velocity:
            self.logger.info("Loaded %d poses with velocity from: %s", len(data), txt_file)
        else:
            self.logger.info("Loaded %d poses from: %s", len(data), txt_file)
        return np.array(data)

    def load_srt_data(self, srt_file: str, conversion_coordinate: str = "utm") -> PosePath3D:
        """
        Load GPS data from an SRT file and convert it to either WGS84 cartesian coordinates or UTM coordinates.

        Args:
            :param srt_file: Path to the SRT file.
            :param conversion_coordinate: Coordinate system to convert the GPS data to. Either 'utm' or 'wgs84'.

        Returns:
            :return: Numpy array in conversion_coordinate.
        """

        srt_data: list[str] = []
        with open(srt_file, encoding="utf-8") as f:
            for sub in srt.parse(f):
                srt_data.append(sub)

        intermediate_content_strings = [sub.content.split(",")[-1] for sub in srt_data]

        lat: list[float] = []
        lon: list[float] = []
        alt: list[float] = []

        # Iterate over each sub_string in intermediate_content_strings
        for sub_string in intermediate_content_strings:
            # Extract the latitude value from the sub_string
            # [latitude:: This part matches the literal string "[latitude:".
            # \s*: This part matches zero or more whitespace characters.
            # (\d+\.\d+): This part is enclosed in parentheses and captures a decimal number.
            #
            # Here's what each component means:
            # \d+: This matches one or more digits.
            # \.: This matches a literal dot character.
            # \d+: This matches one or more digits.
            # \]: This part matches the literal closing square bracket "]" at the end.
            lat.append(float(re.search(r"\[latitude:\s*(\d+\.\d+)\]", sub_string).group(1)))

            # Extract the longitude value from the sub_string
            lon.append(float(re.search(r"\[longitude:\s*(\d+\.\d+)\]", sub_string).group(1)))

            # Extract the relative altitude value from the sub_string
            alt.append(float(re.search(r"\[rel_alt:\s*([-+]?\d+\.\d+).*?\]", sub_string).group(1)))

        if conversion_coordinate == "utm":
            pos_xyz: np.ndarray = transformation.latlonhtoUTM(lat, lon, alt)
        elif conversion_coordinate == "wgs84":
            pos_xyz: np.ndarray = transformation.latlonhtoxyzwgs84(lat, lon, alt)
        else:
            raise ValueError(
                "Unknown conversion format: %s, please select either utm or wgs84",
                conversion_coordinate,
            )

        quat_wxyz: np.ndarray = np.zeros((pos_xyz.shape[0], 4))
        quat_wxyz[:, 0] = 1

        self.logger.info("Loaded %d 3D points from: %s", pos_xyz.shape[0], srt_file)

        return PosePath3D(positions_xyz=np.array(pos_xyz), orientations_quat_wxyz=quat_wxyz)

    def create_custom_tms(self, flight_time: float, size_trajectory: int):
        """
        Create custom timestamps for a trajectory with equal intervals.

        Args:
            :param flight_time: Time of flight in seconds.
            :param size_trajectory: Size of trajectory

        Returns:
            :return: timestamps with equal intervals.
        """
        gap = flight_time / size_trajectory
        tms = np.arange(0.0, flight_time, gap)
        return tms[:size_trajectory]
