import numpy as np
import pypose
from evo.core.trajectory import PoseTrajectory3D


def set_zero_for_origin(traj_np: np.ndarray) -> np.ndarray:
    poses_xyz_xyzw = traj_np[:, [0, 1, 2, 3, 5, 6, 7, 4]]  # tms, tx, ty, tz, qx, qy, qz, qw
    pypose_se3_poses = pypose.SE3(poses_xyz_xyzw[:, 1:])
    first_pose = pypose_se3_poses[0]
    transformed_poses = first_pose.Inv() * pypose_se3_poses

    poses_xyz_wxyz = transformed_poses[:, [0, 1, 2, 6, 3, 4, 5]]  # return to tx, ty, tz, qw, qx, qy, qz
    return np.hstack((traj_np[:, 0].reshape(-1, 1), poses_xyz_wxyz))


def SE3Up_pose_interpolated(fst_np: np.ndarray, sec_np: np.ndarray, u: float) -> list:
    """
    Interpolates between two SE3 poses using SE3Up.
    Ref: Survey of Higher Order Rigid Body Motion Interpolation Methods
         for Keyframe Animation and Continuous-Time Trajectory Estimation
         Section: Special Euclidean twist upsampler (SE3Up), Eq. 26,27

    Args:
        fst_np (list): First pose represented as [x, y, z, qw, qx, qy, qz].
        sec_np (list): Second pose represented as [x, y, z, qw, qx, qy, qz].
        u (float): Interpolation parameter between 0 and 1.

    Returns:
        list: Interpolated pose represented as [x, y, z, qw, qx, qy, qz].
    """

    # convert from xyz+wxyz -> xyz+xyzw
    first_se3_pose = pypose.SE3([fst_np[1], fst_np[2], fst_np[3], fst_np[5], fst_np[6], fst_np[7], fst_np[4]])
    second_se3_pose = pypose.SE3([sec_np[1], sec_np[2], sec_np[3], sec_np[5], sec_np[6], sec_np[7], sec_np[4]])

    relative_pose = first_se3_pose.Inv() * second_se3_pose
    interpolated_pose = first_se3_pose * pypose.Exp(pypose.Log(relative_pose) * u)
    pose_tensor = interpolated_pose.tensor()
    # convert to xyz+wxyz
    return [
        pose_tensor[0],
        pose_tensor[1],
        pose_tensor[2],
        pose_tensor[6],
        pose_tensor[3],
        pose_tensor[4],
        pose_tensor[5],
    ]


def pose_interpolation(sparse_msg_np: np.ndarray, densify_size: int) -> np.ndarray:
    """
    Interpolates poses based on timestamps using SE3Up interpolation.

    Args:
        sparse_msg_np (np.ndarray): Array in numpy with sparse messages.
        densify_size (int): Number of interpolated timestamps to generate.

    Returns:
        np.ndarray: Numpy array containing the interpolated timestamps and poses.

    """
    timestamps = sparse_msg_np[:, 0]  # Assuming the timestamps are in the first column
    min_timestamp = np.min(timestamps)
    max_timestamp = np.max(timestamps)
    interpolated_timestamps = np.linspace(min_timestamp, max_timestamp, densify_size)

    index = 0
    interpolated_tms_poses = []
    for interpolated_tms in interpolated_timestamps:
        while interpolated_tms > timestamps[index + 1]:
            index += 1

        # Calculate the interpolation factor
        u = (interpolated_tms - timestamps[index]) / (timestamps[index + 1] - timestamps[index])

        # Interpolate the pose using SE3Up and interpolation factor
        interpolated_pose = SE3Up_pose_interpolated(sparse_msg_np[index], sparse_msg_np[index + 1], u)
        interpolated_tms_poses.append([interpolated_tms] + interpolated_pose)

    return np.array(interpolated_tms_poses)


def find_closest_indices(timestamps, target) -> tuple[int, int]:
    """
    Find the indices of the two timestamps in an array that are closest to a given target timestamp.

    This function searches for the position where the target timestamp would fit in
    a sorted list of timestamps, and returns the indices of the timestamps immediately
    before and after the target timestamp. If the target timestamp is beyond the range
    of the given timestamps, the indices of the two closest timestamps at the ends
    of the range are returned.

    Parameters:
    - timestamps (np.array): A numpy array of sorted timestamps.
    - target (float): The target timestamp.

    Returns:
    - tuple: A tuple containing the indices of the two closest timestamps.
    """
    if target < timestamps[0]:
        return -1, -1

    idx = np.searchsorted(timestamps, target)
    idx1 = max(idx - 1, 0)
    idx2 = min(idx, len(timestamps) - 1)

    # Adjust indices if they point to the same element and it's not the start,
    # moving idx1 one step back to ensure two distinct indices
    if idx1 == idx2 and idx1 > 0:
        idx1 -= 1
    return idx1, idx2


def sync_local_pose_with_image_timestamps(local_msg_np: np.ndarray, image_tms_np: np.ndarray) -> np.ndarray:
    """
    Synchronize local poses with image timestamps and interpolate poses for these timestamps.

    This function adjusts the origin of local messages to zero and interpolates poses
    based on image timestamps. For each timestamp in the image timestamps array,
    it finds the closest poses before and after this timestamp in the local messages array,
    computes the interpolation factor, and then interpolates the pose.

    Parameters:
    - local_msg_np (np.ndarray): A numpy array of local messages. The first column should be timestamps,
                                 followed by the pose data (translation and rotation).
    - image_tms_np (np.ndarray): A numpy array of image timestamps to be synchronized with the local messages.

    Returns:
    - np.ndarray: A numpy array of interpolated poses for the given image timestamps.

    The function assumes timestamps in microseconds and scales them down for calculations.
    """

    # Adjust the origin of local messages to zero
    local_msg_np_origin = set_zero_for_origin(local_msg_np)
    interpolated_poses = []
    timestamps = local_msg_np_origin[:, 0]

    # Loop through each image timestamp to interpolate poses
    for image_tm in image_tms_np:
        # Find indices of the two closest local messages around the image timestamp
        idx1, idx2 = find_closest_indices(timestamps, image_tm)
        if idx1 == -1 and idx2 == -1:
            interpolated_poses.append([image_tm] + local_msg_np_origin[0][1:].tolist())
            continue

        pose1 = local_msg_np_origin[idx1]
        pose2 = local_msg_np_origin[idx2]

        # Calculate the interpolation factor 'u'
        u = (image_tm - timestamps[idx1]) / (timestamps[idx2] - timestamps[idx1])
        interpolated_pose = SE3Up_pose_interpolated(pose1, pose2, u)

        # Append the interpolated pose along with its timestamp to the list
        interpolated_poses.append([image_tm] + interpolated_pose)
    return np.array(interpolated_poses)


def sync_gt_with_estimated_odometry_msgs(
    gt_msg_np: np.ndarray, est_msg_np: np.ndarray
) -> tuple[PoseTrajectory3D, PoseTrajectory3D]:
    """
    Synchronizes the ground truth and estimated messages based on their timestamps.

    Args:
        gt_msg_np (np.ndarray): Ground truth message numpy array.
        est_msg_np (np.ndarray): Estimated message numpy array.

    Returns:
        Tuple: A tuple containing the estimated trajectory and ground truth trajectory.
    """

    # Compute the timestamps offset
    offset_timestamp = gt_msg_np[0, 0] - est_msg_np[0, 0]
    est_msg_np[:, 0] += offset_timestamp

    # Pose interpolation for estimated msgs by size of gt msgs
    est_msg_np = pose_interpolation(est_msg_np, gt_msg_np.shape[0])

    # Create PoseTrajectory3D objects for the estimated and ground truth trajectories
    est_traj = PoseTrajectory3D(
        positions_xyz=gt_msg_np[:, 1:4], orientations_quat_wxyz=gt_msg_np[:, 4:], timestamps=est_msg_np[:, 0]
    )

    gt_traj = PoseTrajectory3D(
        positions_xyz=est_msg_np[:, 1:4], orientations_quat_wxyz=est_msg_np[:, 4:], timestamps=est_msg_np[:, 0]
    )

    return gt_traj, est_traj


def create_trajectory_from_relative_pose(relative_msg_np: np.ndarray) -> np.ndarray:
    """
    Create an absolute trajectory from a series of relative poses.

    This function takes an array of relative poses and computes the absolute trajectory
    in the world frame. The input is assumed to be a numpy array where each row represents
    a relative pose. The format for each relative pose is [timestamp, x, y, z, qw, qx, qy, qz],
    where 'timestamp' is the time at which the pose is recorded, and [x, y, z, qw, qx, qy, qz]
    represent the position and orientation in quaternion format.

    The function computes the absolute pose at each timestamp by accumulating the relative
    transformations. It starts with an identity pose and then sequentially applies each
    relative transformation to the current pose.

    Parameters:
    relative_msg_np (np.ndarray): A numpy array of shape (N, 8) where each row contains a
                                  timestamp followed by a 7-element pose (x, y, z, qw, qx, qy, qz).

    Returns:
    np.ndarray: A numpy array of the absolute trajectory. Each row in the output array
                represents an absolute pose in the format [timestamp, x, y, z, qw, qx, qy, qz].
    """

    # Initialize the absolute trajectory array
    absolute_trajectory = np.zeros((relative_msg_np.shape[0], 8))

    # Start with the identity pose (no rotation, no translation)
    # For start, it is T_W_0
    current_pose = pypose.SE3([0, 0, 0, 0, 0, 0, 1])  # pypose format: x, y, z, qx, qy, qz, qw

    for i, rel_pose in enumerate(relative_msg_np):
        # Convert input pose to pypose format
        if np.isnan(rel_pose).any():
            continue

        # rel_pose = T_Km1_K (pose from K to k-1)
        rel_pose_pypose = pypose.SE3(
            [rel_pose[1], rel_pose[2], rel_pose[3], rel_pose[5], rel_pose[6], rel_pose[7], rel_pose[4]]
        )

        # current_pose = T_W_Km1 * T_Km1_K
        current_pose = current_pose * rel_pose_pypose

        # Convert the pose back to the original format, add timestamp and store in the trajectory
        absolute_trajectory[i] = np.array(
            [
                rel_pose[0],
                current_pose[0],
                current_pose[1],
                current_pose[2],
                current_pose[6],
                current_pose[3],
                current_pose[4],
                current_pose[5],
            ]
        )

    return absolute_trajectory
