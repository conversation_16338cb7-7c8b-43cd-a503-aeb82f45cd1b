import logging

import numpy as np
from evo.core.trajectory import PoseTrajectory3D
from scipy.interpolate import interp1d

from . import transformation

logger = logging.getLogger(__name__)


def find_timestamp_interval(bigger_msg_np: np.ndarray, smaller_msg_np: np.ndarray) -> np.ndarray:
    """
    Find the timestamp interval between two numpy arrays.
    This function finds the index where the timestamps of bigger_msg_np are
    greater than or equal to the timestamp of smaller_msg_np.
    Finally, the code returns a slice of the bigger_msg_np array starting from the sync_index until the end.
    This slice represents the interval of timestamps between the two arrays.

    Args:
        bigger_msg_np (np.ndarray): Numpy array representing the bigger message.
        smaller_msg_np (np.ndarray): Numpy array representing the smaller message.

    Returns:
        np.ndarray: Numpy array containing the interval of timestamps.

    """
    sync_index = np.argmax(bigger_msg_np[:, 0] >= smaller_msg_np[0, 0])
    if sync_index != 0:
        sync_index -= 1

    return bigger_msg_np[sync_index:]


def timestamp_interpolation(gt_enu_msg_np: np.ndarray, est_enu_msg_np: np.ndarray) -> np.ndarray:
    """
    Interpolate the positions of the ground truth message array based on the timestamps of the estimated message array.

    Args:
        gt_enu_msg_np (np.ndarray): Numpy array representing the ground truth message.
        est_enu_msg_np (np.ndarray): Numpy array representing the estimated message.

    Returns:
        np.ndarray: Numpy array with positions interpolated to match the timestamps of the estimated message.
    """
    # Extract timestamps and positions from the ground truth message
    gt_timestamps = gt_enu_msg_np[:, 0]
    gt_positions = gt_enu_msg_np[:, 1:4]

    # Extract timestamps from the smaller message
    est_timestamps = est_enu_msg_np[:, 0]

    # Interpolate positions of the ground truth message to match the timestamps of the estimated message
    interp_func = interp1d(
        gt_timestamps,
        gt_positions,
        axis=0,
        kind="cubic",
        fill_value="extrapolate",
    )
    interpolated_gt_positions = interp_func(est_timestamps)

    # Create a new array with the smaller timestamps and interpolated positions
    sparsified_array = np.hstack((est_timestamps.reshape(-1, 1), interpolated_gt_positions))

    return sparsified_array


def sync_gt_with_estimated_gps_msgs(
    gt_wgs84_msg_np: np.ndarray,
    est_wgs84_msg_np: np.ndarray,
    coordinate_system: str = "enu",
) -> tuple[PoseTrajectory3D, PoseTrajectory3D, PoseTrajectory3D, PoseTrajectory3D]:
    """
    Synchronizes the ground truth and estimated messages based on their timestamps.

    Args:
        gt_wgs84_msg_np (np.ndarray): Ground truth message numpy array in WGS84 coordinates.
        est_wgs84_msg_np (np.ndarray): Estimated message numpy array in WGS84 coordinates.
        coordinate_system (str): Target coordinate system, either "enu" or "ned". Defaults to "enu".

    Returns:
        tuple: A tuple containing the ground truth and estimated trajectories for local coordinates and WGS84.
    """
    if coordinate_system not in ["enu", "ned"]:
        raise ValueError(f"coordinate_system must be 'enu' or 'ned', got '{coordinate_system}'")

    # Find the overlapping time interval between both trajectories
    gt_start_time, gt_end_time = gt_wgs84_msg_np[0, 0], gt_wgs84_msg_np[-1, 0]
    est_start_time, est_end_time = est_wgs84_msg_np[0, 0], est_wgs84_msg_np[-1, 0]

    # Calculate the overlapping time interval
    overlap_start = max(gt_start_time, est_start_time)
    overlap_end = min(gt_end_time, est_end_time)

    if overlap_start >= overlap_end:
        raise ValueError("No temporal overlap between ground truth and estimated trajectories")

    # Trim ground truth trajectory to overlapping interval
    gt_start_idx = np.argmax(gt_wgs84_msg_np[:, 0] >= overlap_start)
    gt_end_idx = np.searchsorted(gt_wgs84_msg_np[:, 0], overlap_end, side="right")
    gt_wgs84_msg_np_trimmed = gt_wgs84_msg_np[gt_start_idx:gt_end_idx]

    # Trim estimated trajectory to overlapping interval
    est_start_idx = np.argmax(est_wgs84_msg_np[:, 0] >= overlap_start)
    est_end_idx = np.searchsorted(est_wgs84_msg_np[:, 0], overlap_end, side="right")
    est_wgs84_msg_np_trimmed = est_wgs84_msg_np[est_start_idx:est_end_idx]

    logger.info(f"Overlapping interval: {overlap_start:.3f} - {overlap_end:.3f}")
    logger.info(f"Trimmed GT trajectory: {gt_wgs84_msg_np_trimmed.shape[0]} points")
    logger.info(f"Trimmed EST trajectory: {est_wgs84_msg_np_trimmed.shape[0]} points")

    # Create a numpy array of zeros with shape (number of rows in est_msg_np, 4)
    quat_wxyz = np.zeros((est_wgs84_msg_np_trimmed.shape[0], 4))
    quat_wxyz[:, 0] = 1  # Set the first column of quat_wxyz to 1

    # Create PoseTrajectory3D trajectories for the specified coordinate system
    if coordinate_system == "enu":
        est_position_xyz = transformation.wgs84_geodetic_to_enu(
            est_wgs84_msg_np_trimmed[:, 1], est_wgs84_msg_np_trimmed[:, 2], est_wgs84_msg_np_trimmed[:, 3]
        )
        gt_position_xyz = transformation.wgs84_geodetic_to_enu(
            gt_wgs84_msg_np_trimmed[:, 1], gt_wgs84_msg_np_trimmed[:, 2], gt_wgs84_msg_np_trimmed[:, 3]
        )
    else:  # coordinate_system == "ned"
        est_position_xyz = transformation.wgs84_geodetic_to_ned(
            est_wgs84_msg_np_trimmed[:, 1], est_wgs84_msg_np_trimmed[:, 2], est_wgs84_msg_np_trimmed[:, 3]
        )
        gt_position_xyz = transformation.wgs84_geodetic_to_ned(
            gt_wgs84_msg_np_trimmed[:, 1], gt_wgs84_msg_np_trimmed[:, 2], gt_wgs84_msg_np_trimmed[:, 3]
        )

    est_local_msg_np = np.hstack((est_wgs84_msg_np_trimmed[:, 0].reshape(-1, 1), est_position_xyz))
    gt_local_msg_np = np.hstack((gt_wgs84_msg_np_trimmed[:, 0].reshape(-1, 1), gt_position_xyz))

    gt_local_msg_np_interpolated = timestamp_interpolation(gt_local_msg_np, est_local_msg_np)

    est_traj_local = PoseTrajectory3D(
        positions_xyz=est_local_msg_np[:, 1:4],
        orientations_quat_wxyz=quat_wxyz,
        timestamps=est_local_msg_np[:, 0],
    )

    est_traj_wgs84 = PoseTrajectory3D(
        positions_xyz=est_wgs84_msg_np_trimmed[:, 1:4],
        orientations_quat_wxyz=quat_wxyz,
        timestamps=est_wgs84_msg_np_trimmed[:, 0],
    )

    gt_traj_local = PoseTrajectory3D(
        positions_xyz=gt_local_msg_np_interpolated[:, 1:4],
        orientations_quat_wxyz=quat_wxyz,
        timestamps=gt_local_msg_np_interpolated[:, 0],
    )

    # Create identity quaternions for gt_wgs84 trajectory
    gt_wgs84_quat = np.zeros((gt_wgs84_msg_np_trimmed.shape[0], 4))
    gt_wgs84_quat[:, 0] = 1

    gt_traj_wgs84 = PoseTrajectory3D(
        positions_xyz=gt_wgs84_msg_np_trimmed[:, 1:4],
        orientations_quat_wxyz=gt_wgs84_quat,
        timestamps=gt_wgs84_msg_np_trimmed[:, 0],
    )

    return (gt_traj_local, est_traj_local, gt_traj_wgs84, est_traj_wgs84)


def sync_gt_with_estimated_vo_msgs(
    gt_wgs84_msg_np: np.ndarray,
    est_msg_np: np.ndarray,
    coordinate_system: str = "enu",
) -> tuple[PoseTrajectory3D, PoseTrajectory3D]:
    """
    Synchronizes the ground truth and estimated messages based on their timestamps.

    Args:
        gt_wgs84_msg_np (np.ndarray): Ground truth message numpy array in WGS84 coordinates.
        est_msg_np (np.ndarray): Estimated message numpy array in local coordinates (ENU or NED).
        coordinate_system (str): Target coordinate system, either "enu" or "ned". Defaults to "enu".

    Returns:
        Tuple: A tuple containing the estimated trajectory and ground truth trajectories for NED and WGS84.
    """

    # print("gt_wgs84_msg_np shape", gt_wgs84_msg_np.shape)
    # print("first timestamp", gt_wgs84_msg_np[0, 0])
    # print("last timestamp", gt_wgs84_msg_np[-1, 0])
    # print("est_msg_np shape", est_msg_np.shape)
    # print("first timestamp", est_msg_np[0, 0])
    # print("last timestamp", est_msg_np[-1, 0])

    # Find the intersection of timestamps between gt_wgs84_msg_np and est_wgs84_msg_np
    if gt_wgs84_msg_np[0, 0] <= est_msg_np[0, 0]:
        old_size, old_first_timestamp = gt_wgs84_msg_np.shape[0], gt_wgs84_msg_np[0, 0]
        gt_wgs84_msg_np = find_timestamp_interval(gt_wgs84_msg_np, est_msg_np)
        new_size, new_first_timestamp = gt_wgs84_msg_np.shape[0], gt_wgs84_msg_np[0, 0]
        print(f"Size of groud-truth GPS changed: {old_size} -> {new_size} ")
        print(f"Timestamp of groud-truth GPS changed: {old_first_timestamp} -> {new_first_timestamp}")
    else:
        logger.error(
            "We anticipate that the ground truth will have earlier timestamps compared to the estimated trajectory. "
            "In this situation, how does GNSS synchronization estimate the transformation?"
        )

    # print("gt_wgs84_msg_np shape", gt_wgs84_msg_np.shape)
    # print("first timestamp", gt_wgs84_msg_np[0, 0])
    # print("last timestamp", gt_wgs84_msg_np[-1, 0])
    # print("est_msg_np shape", est_msg_np.shape)
    # print("first timestamp", est_msg_np[0, 0])
    # print("last timestamp", est_msg_np[-1, 0])

    # print("Maximum x of gt_wgs84_msg_np", np.max(gt_wgs84_msg_np[:, 1]))
    # print("Maximum y of gt_wgs84_msg_np", np.max(gt_wgs84_msg_np[:, 2]))
    # print("Maximum z of gt_wgs84_msg_np", np.max(gt_wgs84_msg_np[:, 3]))

    # Create a numpy array of zeros with shape (number of rows in est_msg_np, 4)
    quat_wxyz = np.zeros((est_msg_np.shape[0], 4))
    quat_wxyz[:, 0] = 1  # Set the first column of quat_wxyz to 1

    # Create PoseTrajectory3D trajectories for the specified coordinate system
    # if coordinate_system == "enu":
    #     gt_position_xyz = transformation.wgs84_geodetic_to_enu(
    #         gt_wgs84_msg_np[:, 1], gt_wgs84_msg_np[:, 2], gt_wgs84_msg_np[:, 3]
    #     )
    # else:  # coordinate_system == "ned"
    #     gt_position_xyz = transformation.wgs84_geodetic_to_ned(
    #         gt_wgs84_msg_np[:, 1], gt_wgs84_msg_np[:, 2], gt_wgs84_msg_np[:, 3]
    #     )

    # gt_local_msg_np = np.hstack((gt_wgs84_msg_np[:, 0].reshape(-1, 1), gt_position_xyz))

    gt_local_msg_np = gt_wgs84_msg_np

    # print("Maximum x of gt_local_msg_np", np.max(gt_local_msg_np[:, 1]))
    # print("Maximum y of gt_local_msg_np", np.max(gt_local_msg_np[:, 2]))
    # print("Maximum z of gt_local_msg_np", np.max(gt_local_msg_np[:, 3]))
    # print("DONE")

    gt_local_msg_np_interpolated = timestamp_interpolation(gt_local_msg_np, est_msg_np)

    # print("Maximum x of gt_local_msg_np", np.max(gt_local_msg_np[:, 1]))
    # print("Maximum y of gt_local_msg_np", np.max(gt_local_msg_np[:, 2]))
    # print("Maximum z of gt_local_msg_np", np.max(gt_local_msg_np[:, 3]))

    est_traj_local = PoseTrajectory3D(
        positions_xyz=est_msg_np[:, 1:4],
        orientations_quat_wxyz=quat_wxyz,
        timestamps=est_msg_np[:, 0],
    )

    gt_traj_local = PoseTrajectory3D(
        positions_xyz=gt_local_msg_np_interpolated[:, 1:4],
        orientations_quat_wxyz=quat_wxyz,
        timestamps=gt_local_msg_np_interpolated[:, 0],
    )

    return (gt_traj_local, est_traj_local)
