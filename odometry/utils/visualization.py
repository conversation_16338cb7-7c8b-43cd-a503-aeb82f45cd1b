import collections
from collections import Counter, defaultdict

import numpy as np
import plotly.graph_objs as go
import plotly.io as pio
from evo.core import trajectory
from plotly.offline import plot
from plotly.subplots import make_subplots


def plot_3d_single(
    traj: np.ndarray,
    color: str,
    filename: str = "",
    show_plot: bool = False,
):
    """
    Plot a 3D trajectory.

    Args:
        traj (numpy.array): trajectory.
        filename (str, optional): Name of the output file.
        show_plot (bool, optional): Whether to show the plot.
    """
    fig = go.Figure()

    fig.add_trace(
        go.Scatter3d(
            x=traj[:, 1],
            y=traj[:, 2],
            z=traj[:, 3],
            text=[f"{i}: {t}" for i, t in enumerate(traj[:, 0].astype("datetime64[ms]"))],
            name="trajectory",
            marker=dict(color=color, size=4, symbol="circle", opacity=0.8),
            line=dict(color=color, width=2),
        )
    )

    fig.update_layout(
        margin=dict(l=0, r=0, b=0, t=0),
        legend_title_text="Trajectories",
        xaxis_title="x (m)",
        yaxis_title="y (m)",
        title={
            "text": "local trajectories",
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
        },
    )

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def plot_2d_single(
    traj: np.ndarray,
    color: str,
    filename: str = "",
    show_plot: bool = False,
):
    """
    Plot a 2D trajectory.

    Args:
        traj (numpy.array): trajectory.
        color (str): Color of the trajectory line.
        filename (str, optional): Name of the output file.
        show_plot (bool, optional): Whether to show the plot.
    """
    fig = go.Figure()

    fig.add_trace(
        go.Scatter(
            x=traj[:, 0],
            y=traj[:, 1],
            text=traj[:, 0].astype("datetime64[ms]"),
            name="trajectory",
            marker=dict(color=color, size=4, symbol="circle", opacity=0.8),
            line=dict(color=color, width=2),
        )
    )

    fig.update_layout(
        margin=dict(l=0, r=0, b=0, t=0),
        legend_title_text="Trajectories",
        xaxis_title="x (m)",
        yaxis_title="y (m)",
        title={
            "text": "2D Trajectory",
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
        },
    )

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def geo_map_single(
    traj: np.ndarray,
    filename: str = "",
    title: str = "GPS Track",
    color: str = "red",
    show_plot: bool = False,
):
    """
    Generates a plotly map with GPS traces based on the given topic messages.

    Args:
        traj (np.ndarray): Trajectory data.
        filename (str): Filename to save the plot.
        title (str): Title of the plot.
        color (str): Color of the trajectory line.
        show_plot (bool, optional): Whether to show the plot.
    Returns:
        None
    """

    fig = go.Figure()

    fig.add_trace(
        go.Scattermapbox(
            lat=traj[:, 1],
            lon=traj[:, 2],
            text=traj[:, 0].astype("datetime64[ms]"),
            name=title,
            line=dict(color=color, width=2.0),
            showlegend=True,
        )
    )

    # Set the GPS boundary margin in degrees
    GPS_BOUNDARY_MARGIN_IN_DEGREE = 0.007

    # Update the mapbox settings
    fig.update_mapboxes(
        bounds=dict(
            west=np.amin(traj[:, 2]) - GPS_BOUNDARY_MARGIN_IN_DEGREE,
            east=np.amax(traj[:, 2]) + GPS_BOUNDARY_MARGIN_IN_DEGREE,
            south=np.amin(traj[:, 1]) - GPS_BOUNDARY_MARGIN_IN_DEGREE,
            north=np.amax(traj[:, 1]) + GPS_BOUNDARY_MARGIN_IN_DEGREE,
        ),
        pitch=0,
        zoom=5,
        style="open-street-map",
    )

    fig.update_layout(
        title={
            "text": title,
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
        },
    )

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def plot_3d_drift(
    gt_traj: trajectory.PoseTrajectory3D,
    es_traj: trajectory.PoseTrajectory3D,
    timestamps: np.ndarray,
    positions: np.ndarray,
    title: str = "Drift",
    filename: str = "",
    show_plot: bool = False,
):
    """
    Plot a 3D trajectory.

    Args:
        gt_traj (PoseTrajectory3D): Ground truth trajectory.
        es_traj (PoseTrajectory3D): Estimated trajectory.
        title (str, optional): Title of the plot.
        filename (str, optional): Name of the output file.
        show_plot (bool, optional): Whether to show the plot.
    """

    fig = go.Figure()

    gt_pos = gt_traj.positions_xyz
    es_pos = es_traj.positions_xyz
    drift_timestamps = timestamps
    drift_positions = positions

    fig.add_trace(
        go.Scatter3d(
            x=gt_pos[:, 0],
            y=gt_pos[:, 1],
            z=gt_pos[:, 2],
            text=[f"{i}: {t}" for i, t in enumerate(gt_traj.timestamps.astype("datetime64[ms]"))],
            name="gt_trajectory",
            marker=dict(color="gray", size=4, symbol="circle", opacity=0.8),
            line=dict(color="gray", width=2),
        )
    )

    fig.add_trace(
        go.Scatter3d(
            x=es_pos[:, 0],
            y=es_pos[:, 1],
            z=es_pos[:, 2],
            text=[f"{i}: {t}" for i, t in enumerate(es_traj.timestamps.astype("datetime64[ms]"))],
            name="es_trajectory",
            marker=dict(color="green", size=4, symbol="circle", opacity=0.8),
            line=dict(color="green", width=2),
        )
    )

    fig.add_trace(
        go.Scatter3d(
            x=drift_positions[:, 0],
            y=drift_positions[:, 1],
            z=drift_positions[:, 2],
            text=[f"{i}: {t}" for i, t in enumerate(drift_timestamps.astype("datetime64[ms]"))],
            name="drift_trajectory",
            marker=dict(color="red", size=4, symbol="circle", opacity=0.8),
            line=dict(color="red", width=2),
        )
    )

    fig.update_layout(
        margin=dict(l=0, r=0, b=0, t=0),
        legend_title_text="Trajectories",
        xaxis_title="x (m)",
        yaxis_title="y (m)",
        title={
            "text": title,
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
        },
    )

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def plot_3d(
    gt_traj: trajectory.PoseTrajectory3D,
    es_traj: trajectory.PoseTrajectory3D,
    error: np.ndarray,
    error_type: str,
    topic_msgs: dict[str, np.ndarray] | None = None,
    topic_dict: dict[str, str] | None = None,
    title: str = "Error",
    filename: str = "",
    show_plot: bool = False,
):
    """
    Plot a 3D trajectory.

    Args:
        gt_traj (PoseTrajectory3D): Ground truth trajectory.
        es_traj (PoseTrajectory3D): Estimated trajectory.
        error (numpy.ndarray): Error array.
        topic_msgs (dict, optional): Dictionary of topic messages.
        topic_dict (dict, optional): Dictionary of topic names.
        title (str, optional): Title of the plot.
        filename (str, optional): Name of the output file.
        show_plot (bool, optional): Whether to show the plot.
    """

    fig = go.Figure()

    gt_pos = gt_traj.positions_xyz
    es_pos = es_traj.positions_xyz

    fig.add_trace(
        go.Scatter3d(
            x=gt_pos[:, 0],
            y=gt_pos[:, 1],
            z=gt_pos[:, 2],
            text=[f"{i}: {t}" for i, t in enumerate(gt_traj.timestamps.astype("datetime64[ms]"))],
            name="gt_trajectory",
            marker=dict(color="gray", size=4, symbol="circle", opacity=0.8),
            line=dict(color="gray", width=2),
        )
    )

    fig.add_trace(
        go.Scatter3d(
            x=es_pos[:, 0],
            y=es_pos[:, 1],
            z=es_pos[:, 2],
            text=[f"{i}: {t}" for i, t in enumerate(es_traj.timestamps.astype("datetime64[ms]"))],
            name="es_trajectory",
            marker=dict(
                color=error,
                colorscale="Viridis",
                colorbar=dict(title=error_type, x=0.95),
                size=4,
                symbol="circle",
                opacity=0.8,
            ),
            line=dict(color="green", width=2),
        )
    )

    fig.update_layout(
        margin=dict(l=0, r=0, b=0, t=0),
        legend_title_text="Trajectories",
        xaxis_title="x (m)",
        yaxis_title="y (m)",
        title={
            "text": title,
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
            "font": {"size": 22},
        },
        width=1920,
        height=1080,
        font=dict(size=18),
    )

    # Function to add a trace to the 3d figure
    def add_trace(poses: np.ndarray, name: str, marker: dict[str, any] | None = None) -> None:
        """
        Adds a trace to the figure.

        Args:
            pose (numpy.ndarray): GPS pose data.
            name (str): Name of the trace.
            marker (dict, optional): Marker settings. Defaults to None.

        Returns:
            None
        """

        # For each status timestamp, find the closest timestamp from the estimated trajectory in enu,
        indexes_of_closest_values = []
        for pose in poses:
            index_of_closest_value = np.argmin(np.abs(es_traj.timestamps - pose[0]))
            indexes_of_closest_values.append(index_of_closest_value)

        # Find the new location of each status topic, from the estimated trajectory
        new_location_enu = es_pos[indexes_of_closest_values]

        fig.add_trace(
            go.Scatter3d(
                x=new_location_enu[:, 0],
                y=new_location_enu[:, 1],
                z=new_location_enu[:, 2],
                text=poses[:, 0].astype("datetime64[ms]"),
                name=name,
                marker=marker,
                mode="markers",
                showlegend=True,
            )
        )

    if topic_msgs is not None and topic_dict is not None:
        # Get the status topic
        for status in topic_dict["status_topics"]:
            msg = topic_msgs[status]
            if msg.shape[0]:
                add_trace(msg, status, marker=dict(size=8, symbol="circle", opacity=0.8))

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def plot_3d_cov(
    cov_msgs: np.ndarray,
    title: str = "Error",
    filename: str = "",
    show_plot: bool = False,
):
    """
    Plot a 3D trajectory based on covariance messages.

    Args:
        cov_msgs (np.ndarray): An array of messages for the covariance topic
        title (str, optional): Title of the plot. Defaults to "Error".
        filename (str, optional): Name of the output file. If empty, the plot is not saved to a file.
        show_plot (bool, optional): Whether to show the plot.
    """
    fig = go.Figure()

    # 8, 15, and 22, are the indices of sigma(xx), sigma(yy), and sigma(zz)
    translation_cov_msgs = cov_msgs[:, [8, 15, 22]]

    # Compute the maximum value for normalization
    tra_cov_msgs_max = np.max(translation_cov_msgs)

    # Prepare the data for plotting
    x_data, y_data, z_data = cov_msgs[:, 1], cov_msgs[:, 2], cov_msgs[:, 3]

    MAGNIFICATION_FACTOR = 50.0
    sizes, colors = [], []

    # Find the biggest value among x, y, z and normalize it
    for msg in translation_cov_msgs:
        max_val = np.amax(msg)  # Find the maximum value
        if (msg > 0).all():  # Check if all sigmas are positive
            normalized_val = max_val / tra_cov_msgs_max  # Normalize
            sizes.append(normalized_val * MAGNIFICATION_FACTOR)
            colors.append(normalized_val)  # Use normalized value for color as well
        else:
            sizes.append(MAGNIFICATION_FACTOR)  # Use maximum value for invalid cov
            colors.append("black")  # Use NaN to later set color to black

    fig.add_trace(
        go.Scatter3d(
            x=x_data,
            y=y_data,
            z=z_data,
            text=cov_msgs[:, 0].astype("datetime64[ms]"),
            line=dict(color="gray", width=2),
            marker=dict(
                size=sizes,
                color=colors,  # set color to normalized values
                colorscale="Viridis",  # choose a colorscale
                colorbar=dict(title="Normalized covariance", x=0.95),
                opacity=0.5,
                symbol="circle",
            ),
        )
    )

    fig.update_layout(
        margin=dict(l=0, r=0, b=0, t=0),
        scene=dict(xaxis_title="X", yaxis_title="Y", zaxis_title="Z"),
        legend_title_text="Covariance",
        title={
            "text": title,
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
        },
    )

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def geo_map_error(
    topic_msgs: dict[str, np.ndarray],
    topic_dict: dict[str, str],
    error: np.ndarray,
    bag_filename: str,
    filename: str,
    show_plot: bool = False,
):
    """
    Generates a plotly map with GPS error map. The color represents the APM(m) error

    Args:
        topic_msgs (dict): Dictionary of topic messages.
        topic_dict (dict): Topic names.
        error (np.ndarray): Error array
        bag_filename (str): Bag filename.
        filename (str): Filename to save the plot.
        show_plot (bool, optional): Whether to show the plot.
    Returns:
        None
    """

    fig = go.Figure()
    gt_pose = topic_msgs[topic_dict["gt_topic"]]
    fig.add_trace(
        go.Scattermapbox(
            lat=gt_pose[:, 1],
            lon=gt_pose[:, 2],
            text=gt_pose[:, 0].astype("datetime64[ms]"),
            name="Error map",
            marker=dict(
                color=error,
                colorscale="Turbo",
                colorbar=dict(title="APE(m)", x=0.97),
                opacity=0.9,
            ),
            showlegend=True,
        )
    )

    # Set the GPS boundary margin in degrees
    GPS_BOUNDARY_MARGIN_IN_DEGREE = 0.003

    # Update the mapbox settings
    fig.update_mapboxes(
        bounds=dict(
            west=np.amin(gt_pose[:, 2]) - GPS_BOUNDARY_MARGIN_IN_DEGREE,
            east=np.amax(gt_pose[:, 2]) + GPS_BOUNDARY_MARGIN_IN_DEGREE,
            south=np.amin(gt_pose[:, 1]) - GPS_BOUNDARY_MARGIN_IN_DEGREE,
            north=np.amax(gt_pose[:, 1]) + GPS_BOUNDARY_MARGIN_IN_DEGREE,
        ),
        pitch=0,
        zoom=10,
        style="open-street-map",
    )

    fig.update_layout(
        title={
            "text": f"GPS Error Map for bag file: {bag_filename}",
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
        },
    )

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def geo_map_tracking(
    tracking_np_wgs84: np.ndarray,
    gt_np_wgs84: np.ndarray,
    bag_filename: str,
    filename: str,
    show_plot: bool = False,
):
    """
    Generates a plotly map with GPS traces based on the given topic messages.

    Args:
        tracking_traj_wgs84 (PoseTrajectory3D): Tracking trajectory in WGS84.
        gt_traj_wgs84 (PoseTrajectory3D): Ground truth trajectory in WGS84.
        bag_filename (str): Bag filename.
        filename (str): Filename to save the plot.
        show_plot (bool, optional): Whether to show the plot.

    Returns:
        None
    """

    fig = go.Figure()

    # Function to add a trace to the figure
    def add_trace(
        pose: np.ndarray,
        name: str,
        color: str,
        marker: dict[str, any] | None = None,
        mode: str = "markers",
    ) -> None:
        """
        Adds a trace to the figure.

        Args:
            pose (numpy.ndarray): GPS pose data.
            name (str): Name of the trace.
            color (str): Color of the trace.
            marker (dict, optional): Marker settings. Defaults to None.
            mode (str, optional): Mode setting. Defaults to "markers".

        Returns:
            None
        """
        if pose.shape[0]:
            fig.add_trace(
                go.Scattermapbox(
                    lat=pose[:, 1],
                    lon=pose[:, 2],
                    text=pose[:, 0].astype("datetime64[ms]"),
                    name=name,
                    line=dict(color=color, width=2.0),
                    marker=marker,
                    mode=mode,
                    showlegend=True,
                )
            )

    # Get the ground truth gps pose
    gt_pose = gt_np_wgs84
    if gt_pose.shape[0]:
        add_trace(gt_pose, "GPS", "RoyalBlue", None, "lines+markers")

    # Get the estimated gps pose
    tracking_pose = tracking_np_wgs84
    if tracking_pose.shape[0]:
        add_trace(tracking_pose, "KTH", "LimeGreen", None, "lines+markers")

    # Set the GPS boundary margin in degrees
    GPS_BOUNDARY_MARGIN_IN_DEGREE = 0.01

    # Update the mapbox settings
    fig.update_mapboxes(
        bounds=dict(
            west=np.amin(gt_pose[:, 2]) - GPS_BOUNDARY_MARGIN_IN_DEGREE,
            east=np.amax(gt_pose[:, 2]) + GPS_BOUNDARY_MARGIN_IN_DEGREE,
            south=np.amin(gt_pose[:, 1]) - GPS_BOUNDARY_MARGIN_IN_DEGREE,
            north=np.amax(gt_pose[:, 1]) + GPS_BOUNDARY_MARGIN_IN_DEGREE,
        ),
        pitch=0,
        zoom=2,
        style="open-street-map",
    )

    fig.update_layout(
        title={
            "text": f"GPS vs KTH for bag file: {bag_filename}",
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
        },
    )

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def geo_map(
    topic_msgs: dict[str, np.ndarray],
    topic_dict: dict[str, str],
    bag_filename: str,
    filename: str,
    show_plot: bool = False,
):
    """
    Generates a plotly map with GPS traces based on the given topic messages.

    Args:
        topic_msgs (dict): Dictionary of topic messages.
        topic_dict (dict): Topic names.
        bag_filename (str): Bag filename.
        filename (str): Filename to save the plot.
        show_plot (bool, optional): Whether to show the plot.

    Returns:
        None
    """

    fig = go.Figure()

    # Function to add a trace to the figure
    def add_trace(
        pose: np.ndarray,
        name: str,
        color: str,
        marker: dict[str, any] | None = None,
        mode: str = "markers",
    ) -> None:
        """
        Adds a trace to the figure.

        Args:
            pose (numpy.ndarray): GPS pose data.
            name (str): Name of the trace.
            color (str): Color of the trace.
            marker (dict, optional): Marker settings. Defaults to None.
            mode (str, optional): Mode setting. Defaults to "markers".

        Returns:
            None
        """
        if pose.shape[0]:
            fig.add_trace(
                go.Scattermapbox(
                    lat=pose[:, 1],
                    lon=pose[:, 2],
                    text=pose[:, 0].astype("datetime64[ms]"),
                    name=name,
                    line=dict(color=color, width=2.0),
                    marker=marker,
                    mode=mode,
                    showlegend=True,
                )
            )

    # Get the ground truth gps pose
    gt_pose = topic_msgs[topic_dict["gt_topic"]]
    if gt_pose.shape[0]:
        add_trace(gt_pose, "Ground-Truth GPS", "mediumspringgreen", None, "lines+markers")

    # Get the estimated gps pose
    est_pose = topic_msgs[topic_dict["sync_topic"]]
    if est_pose.shape[0]:
        add_trace(est_pose, "Estimates GPS", "orangered", None, "lines+markers")

    # Get the status topic
    for status in topic_dict["status_topics"]:
        msg = topic_msgs[status]
        if msg.shape[0]:
            add_trace(msg, status, None, marker=dict(size=12))

    # Set the GPS boundary margin in degrees
    GPS_BOUNDARY_MARGIN_IN_DEGREE = 0.03

    # Update the mapbox settings
    fig.update_mapboxes(
        bounds=dict(
            west=np.amin(gt_pose[:, 2]) - GPS_BOUNDARY_MARGIN_IN_DEGREE,
            east=np.amax(gt_pose[:, 2]) + GPS_BOUNDARY_MARGIN_IN_DEGREE,
            south=np.amin(gt_pose[:, 1]) - GPS_BOUNDARY_MARGIN_IN_DEGREE,
            north=np.amax(gt_pose[:, 1]) + GPS_BOUNDARY_MARGIN_IN_DEGREE,
        ),
        pitch=0,
        zoom=5,
        style="open-street-map",
    )

    fig.update_layout(
        title={
            "text": f"GPS Track Comparison for bag file: {bag_filename}",
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
        },
    )

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def multiple_trajectories(
    gt_traj: trajectory.PoseTrajectory3D,
    es_traj: trajectory.PoseTrajectory3D,
    title: str,
    filename: str,
    show_plot: bool = False,
):
    """
    Compare multiple trajectories and generate a visualization with 3 subplots showing different 2D projections.

    Parameters:
        gt_traj (PoseTrajectory3D): The ground truth trajectory.
        es_traj (PoseTrajectory3D): The estimated trajectory.
        title (str): Title of plot
        filename (str): The filename of the generated visualization.
        show_plot (bool, optional): Whether to show the plot.

    Returns:
        None.
    """
    # Create figure with 3 vertically stacked subplots
    fig = make_subplots(
        rows=1,
        cols=3,
        subplot_titles=("X-Y Plane", "X-Z Plane", "Y-Z Plane"),
    )

    gt_pos = gt_traj.positions_xyz
    est_pos = es_traj.positions_xyz

    # X-Y plot (top)
    fig.add_trace(
        go.Scatter(
            x=gt_pos[:, 0],
            y=gt_pos[:, 1],
            text=gt_traj.timestamps.astype("datetime64[ms]"),
            name="Ground Truth",
            marker_color="gray",
            mode="lines",
        ),
        row=1,
        col=1,
    )
    fig.add_trace(
        go.Scatter(
            x=est_pos[:, 0],
            y=est_pos[:, 1],
            text=es_traj.timestamps.astype("datetime64[ms]"),
            name="Estimated (X-Y)",
            marker_color="red",
            mode="lines",
        ),
        row=1,
        col=1,
    )

    # X-Z plot (middle)
    fig.add_trace(
        go.Scatter(
            x=gt_pos[:, 0],
            y=gt_pos[:, 2],
            text=gt_traj.timestamps.astype("datetime64[ms]"),
            name="Ground Truth",
            marker_color="gray",
            mode="lines",
            showlegend=False,
        ),
        row=1,
        col=2,
    )
    fig.add_trace(
        go.Scatter(
            x=est_pos[:, 0],
            y=est_pos[:, 2],
            text=es_traj.timestamps.astype("datetime64[ms]"),
            name="",
            marker_color="green",
            mode="lines",
        ),
        row=1,
        col=2,
    )

    # Y-Z plot (bottom)
    fig.add_trace(
        go.Scatter(
            x=gt_pos[:, 1],
            y=gt_pos[:, 2],
            text=gt_traj.timestamps.astype("datetime64[ms]"),
            name="Ground Truth",
            marker_color="gray",
            mode="lines",
            showlegend=False,
        ),
        row=1,
        col=3,
    )
    fig.add_trace(
        go.Scatter(
            x=est_pos[:, 1],
            y=est_pos[:, 2],
            text=es_traj.timestamps.astype("datetime64[ms]"),
            name="",
            marker_color="blue",
            mode="lines",
        ),
        row=1,
        col=3,
    )

    # Update layout
    fig.update_layout(
        height=900,  # Increased height for better visibility
        title={
            "text": f"Trajectory Projections: {title}",
            "y": 0.95,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
            "font": {"size": 30},
        },
        font=dict(size=25),
    )

    # Update subplot title font sizes
    fig.layout.annotations[0].update(font=dict(size=30))  # X-Y Plane
    fig.layout.annotations[1].update(font=dict(size=30))  # X-Z Plane
    fig.layout.annotations[2].update(font=dict(size=30))  # Y-Z Plane

    # Update axes labels
    fig.update_xaxes(title_text="X (m)", row=1, col=1)
    fig.update_yaxes(title_text="Y (m)", row=1, col=1)
    fig.update_xaxes(title_text="X (m)", row=1, col=2)
    fig.update_yaxes(title_text="Z (m)", row=1, col=2)
    fig.update_xaxes(title_text="Y (m)", row=1, col=3)
    fig.update_yaxes(title_text="Z (m)", row=1, col=3)

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def plot_xyz_error(
    gt_traj_enu: trajectory.PoseTrajectory3D,
    est_traj_enu: trajectory.PoseTrajectory3D,
    title: str,
    filename: str,
    gt_traj_wgs84: trajectory.PoseTrajectory3D | None = None,
    est_traj_wgs84: trajectory.PoseTrajectory3D | None = None,
    show_plot: bool = False,
) -> None:
    """
    Plots the comparison between estimated and ground-truth trajectories in both
    WGS84 and ENU coordinate systems. The plot displays errors in X, Y, and Z coordinates
    over time for each system.

    Parameters:
        gt_traj_enu (PoseTrajectory3D): Ground truth trajectory in ENU coordinates.
        est_traj_enu (PoseTrajectory3D): Estimated trajectory in ENU coordinates.
        title (str): Title of the plot, describing the comparison being made.
        filename (str): Filename (including path) where the plot will be saved.
        gt_traj_wgs84 (PoseTrajectory3D, optional): Ground truth trajectory in WGS84 coordinates.
        est_traj_wgs84 (PoseTrajectory3D, optional): Estimated trajectory in WGS84 coordinates.

    This function generates a six-panel plot, with three rows corresponding to the latitude (X),
    longitude (Y), and altitude (Z) coordinates. Each row contains two columns: the left column
    displays the data in WGS84 coordinates (latitude, longitude, altitude), when the wgs84 trajectories are available
    and the right column displays the data in Cartesian topocentric ENU (East, North, Up) coordinates. Each panel plots
    both the estimated and ground-truth trajectories to facilitate direct comparison of their accuracy.

    Returns:
        None.
    """

    # Decide on subplot titles based on the availability of WGS84 trajectories
    if gt_traj_wgs84 and est_traj_wgs84:
        subtitles = (
            "WGS84 Latitude",
            "ENU X",
            "WGS84 Longitude",
            "ENU Y",
            "WGS84 Altitude",
            "ENU Z",
        )
    else:
        subtitles = ("", "ENU X", "", "ENU Y", "", "ENU Z")

    # Initialize the figure with 3 rows and 2 columns for different coordinate comparisons
    fig = make_subplots(
        rows=3,
        cols=2,
        subplot_titles=subtitles,
    )
    colors = ["red", "green", "blue"]  # Colors for different axis plots
    labels = ["Latitude", "X", "Longitude", "Y", "Altitude", "Z"]
    trajectories = [est_traj_wgs84, gt_traj_wgs84, est_traj_enu, gt_traj_enu]
    y_axis_labels = [
        "latitude (degree)",
        "x (m)",
        "longitude (degree)",
        "y (m)",
        "altitude (m)",
        "z (m)",
    ]

    # Loop through the three coordinates (latitude/longitude/altitude or X/Y/Z)
    for i in range(3):
        for j in range(2):  # Iterate over the two columns for WGS84 and ENU
            row = i + 1
            col = j + 1

            # Skip plotting for WGS84 if trajectories are not provided (in RPE).
            if j == 0 and (gt_traj_wgs84 is None or est_traj_wgs84 is None):
                continue

            # Add scatter plot for estimated trajectory
            fig.add_trace(
                go.Scatter(
                    x=trajectories[j * 2].timestamps,
                    y=trajectories[j * 2].positions_xyz[:, i],  # Selecting estimated trajectory
                    name=f"Estimated {labels[i * 2 + j]}",
                    marker_color=colors[i],
                    mode="lines",
                    legendgroup=str(i * 2 + j),  # Grouping for legend clarity
                ),
                row=row,
                col=col,
            )

            # Add scatter plot for ground truth trajectory
            fig.add_trace(
                go.Scatter(
                    x=trajectories[j * 2 + 1].timestamps,
                    y=trajectories[j * 2 + 1].positions_xyz[:, i],  # Selecting ground truth trajectory
                    name=f"Ground-Truth {labels[i * 2 + j]}",
                    marker_color="gray",
                    mode="lines",
                    legendgroup=str(i * 2 + j),  # Ensure legend groups match for comparison
                ),
                row=row,
                col=col,
            )
            # Update y-axis labels to be informative
            fig.update_yaxes(title_text=y_axis_labels[i * 2 + j], row=row, col=col)

    # Set x-axis labels on the bottom plots to indicate they represent time
    fig.update_xaxes(title_text="Time", row=3, col=1)
    fig.update_xaxes(title_text="Time", row=3, col=2)
    # General layout updates for the entire figure
    fig.update_layout(
        showlegend=True,  # Display legend for easy identification
        title=dict(text=title, y=0.98, x=0.5, xanchor="center", yanchor="top"),
    )
    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def plot_xyz_tracking_error(
    gt_np: np.ndarray,
    tracking_np: np.ndarray,
    title: str,
    filename: str,
    show_plot: bool = False,
) -> None:
    """
    Plots the comparison between tracking and ground-truth trajectories in WGS84 coordinate system.
    The plot displays latitude, longitude, and altitude over time.

    Parameters:
        gt_np (np.ndarray): Ground truth trajectory in WGS84 coordinates.
        tracking_np (np.ndarray): Tracking trajectory in WGS84 coordinates.
        title (str): Title of the plot, describing the comparison being made.
        filename (str): Filename (including path) where the plot will be saved.

    This function generates a three-panel plot, with rows corresponding to the latitude,
    longitude, and altitude. Each panel plots both the tracking and ground-truth trajectories
    to facilitate direct comparison of their accuracy.

    Returns:
        None.
    """

    # Print the size of gt_np and tracking_np
    print(f"Size of gt_np: {gt_np.shape}")
    print(f"Size of tracking_np: {tracking_np.shape}")

    # Calculate the difference in length between gt_np and tracking_np timestamps
    gt_length = len(gt_np[:, 0])
    tracking_length = len(tracking_np[:, 0])
    length_diff = abs(gt_length - tracking_length)

    print(f"Length of ground truth timestamps: {gt_length}")
    print(f"Length of tracking timestamps: {tracking_length}")
    print(f"Difference in timestamp lengths: {length_diff}")

    # Calculate the time range for both trajectories
    gt_time_range = gt_np[-1, 0] - gt_np[0, 0]
    tracking_time_range = tracking_np[-1, 0] - tracking_np[0, 0]

    print(f"Time range of ground truth trajectory: {gt_time_range} ms")
    print(f"Time range of tracking trajectory: {tracking_time_range} ms")

    # Calculate and print the time difference between trajectories
    time_diff = abs(gt_time_range - tracking_time_range)
    print(f"Difference in time ranges: {time_diff} ms")

    # Initialize the figure with 3 rows and 1 column
    fig = make_subplots(
        rows=3,
        cols=1,
        subplot_titles=("Latitude", "Longitude", "Altitude"),
        shared_xaxes=True,
        vertical_spacing=0.1,
    )

    # Convert timestamps for plotting
    gt_timestamps = gt_np[:, 0].astype("datetime64[ms]")
    tracking_timestamps = tracking_np[:, 0].astype("datetime64[ms]")

    tracking_timestamps = tracking_np[:, 0].astype("datetime64[ms]")

    # Update the tracking_np array with the new timestamps
    tracking_np[:, 0] = tracking_timestamps.astype(np.int64)

    print("Updated tracking timestamps:")
    print(f"First timestamp: {tracking_timestamps[0]}")
    print(f"Last timestamp: {tracking_timestamps[-1]}")
    print(f"New time range: {tracking_timestamps[-1] - tracking_timestamps[0]}")

    y_axis_labels = ["Latitude (degree)", "Longitude (degree)", "Altitude (m)"]

    # Loop through the three coordinates (latitude, longitude, altitude)
    for i in range(3):
        # Add scatter plot for ground truth trajectory
        fig.add_trace(
            go.Scatter(
                x=gt_timestamps,
                y=gt_np[:, i + 1],
                name=f"GPS {y_axis_labels[i]}",
                marker_color="RoyalBlue",
                mode="lines+markers",
            ),
            row=i + 1,
            col=1,
        )

        # Add scatter plot for tracking trajectory
        fig.add_trace(
            go.Scatter(
                x=tracking_timestamps,
                y=tracking_np[:, i + 1],
                name=f"KTH {y_axis_labels[i]}",
                marker_color="LimeGreen",
                mode="lines+markers",
            ),
            row=i + 1,
            col=1,
        )

        # Update y-axis labels
        fig.update_yaxes(title_text=y_axis_labels[i], row=i + 1, col=1)

    # Set x-axis label on the bottom plot to indicate it represents time
    fig.update_xaxes(title_text="Time", row=3, col=1)

    # General layout updates for the entire figure
    fig.update_layout(
        showlegend=True,  # Display legend for easy identification
        title=dict(text=title, y=0.98, x=0.5, xanchor="center", yanchor="top"),
        height=900,  # Increase the height of the figure
    )

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def plot_2d_error_array(
    result,
    timestamps: np.ndarray,
    topic_msgs: dict[str, np.ndarray],
    topic_dict: dict[str, any],
    error_type: str,
    title: str,
    filename: str,
    show_plot: bool = False,
) -> None:
    """
    Plot representing the pose error (APE or RPE), relevant statistical information and
    some topics messages from the bag file

    Args:
        result: APE or RPE result.
        timestamps (numpy.ndarray): Timestamps.
        topic_msgs (dict): Dictionary of topic messages.
        topic_dict (dict): Dictionary of topic names.
        title (str): Title of plot
        filename (str): Name of the output file.
    """

    # Create figure and add line
    x_array = timestamps.astype("datetime64[ms]")

    errors = result.np_arrays["error_array"]

    fig = go.Figure()
    fig.add_trace(go.Scatter(x=x_array, y=errors, line_color="gray", line_width=2, name=error_type))
    fig.update_xaxes(rangeslider_visible=True)

    mean, std = result.stats["mean"], result.stats["std"]
    fig.add_hrect(
        y0=mean - std / 2,
        y1=mean + std / 2,
        line_width=0,
        fillcolor="mediumpurple",
        opacity=0.2,
        name="std",
    )

    fig.add_hline(
        y=result.stats["mean"],
        line_width=3,
        line_color="darkorange",
        name="mean",
    )
    fig.add_hline(y=result.stats["rmse"], line_width=3, line_color="navy", name="rmse")
    fig.add_hline(
        y=result.stats["median"],
        line_width=3,
        line_color="gold",
        name="median",
    )

    for topic in topic_dict["status_topics"]:
        msgs = topic_msgs.get(topic)
        # msgs variable is an array or a NumPy array, and when you use it in the if statement,
        # Python is unable to determine its truth value because it has more than one element.
        if isinstance(msgs, np.ndarray):
            x, y = [], []
            for msg in msgs:
                timestamp = msg[0].astype("datetime64[ms]")
                x.extend([timestamp, timestamp, None])
                y.extend([errors.min(), errors.max(), None])

            fig.add_trace(
                go.Scatter(
                    x=x,
                    y=y,
                    line_width=3,
                    name=topic,
                    line_dash="dot",
                    legendgroup=topic,
                    connectgaps=False,
                )
            )

    fig.add_trace(go.Scatter(x=[None], y=[None], name="std", line_color="mediumpurple"))
    fig.add_trace(go.Scatter(x=[None], y=[None], name="mean", line_color="darkorange"))
    fig.add_trace(go.Scatter(x=[None], y=[None], name="median", line_color="gold"))

    fig.update_layout(
        xaxis_title="Time",
        yaxis_title=result.info["label"],
        showlegend=True,
        title={
            "text": title,
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
        },
    )

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def plot_frequency(
    topic_timestamps: collections.defaultdict,
    title: str,
    filename: str,
    show_plot: bool = False,
) -> None:
    """
    Plot representing the average frequency of message publishing for relevant topics

    Args:
        topic_timestamps (defaultdict): Dictionary of topic:timestamps[list] pairs.
        title (str): Title of plot
        filename (str): Name of the output file.
        show_plot (bool, optional): Whether to show the plot.
    """

    def segment_timestamps(timestamps_dict, interval_length):
        """
        Segment the timestamps into intervals.
        """
        segmented_data = defaultdict(Counter)
        for topic, timestamps in timestamps_dict.items():
            for t in timestamps:
                interval = int(t / (interval_length * 1e9))  # Convert interval_length to nanoseconds
                segmented_data[topic][interval] += 1

        return segmented_data

    fig = go.Figure()
    interval_length = 1

    for topic, intervals in segment_timestamps(topic_timestamps, interval_length=interval_length).items():
        # Convert interval indices to real time in seconds
        times = [interval * interval_length for interval in intervals.keys()]
        frequencies = [count / interval_length for count in intervals.values()]

        x = np.asarray(times).astype("datetime64[s]")
        y = frequencies
        fig.add_trace(go.Scatter(x=x, y=y, line_width=2, name=topic))

    fig.update_xaxes(rangeslider_visible=True)

    fig.update_layout(
        xaxis_title="Time",
        yaxis_title="Frequency (Hz)",
        showlegend=True,
        title={
            "text": title,
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
        },
    )

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def plot_scale(
    topic_msgs: dict[str, np.ndarray],
    topic_dict: dict[str, any],
    title: str,
    filename: str,
    show_plot: bool = False,
) -> None:
    """
    Plot the scale topic over time.

    Args:
        topic_msgs (dict): Dictionary of topic messages.
        topic_dict (dict): Dictionary of topic names.
        title (str): Title of plot
        filename (str): Name of the output file.
        show_plot (bool, optional): Whether to show the plot.
    """

    fig = go.Figure()

    scale_topic = topic_msgs[topic_dict["scale_topic"]]

    timestamps = scale_topic[:, 0].astype("datetime64[ms]")
    scales = scale_topic[:, 1]

    fig.add_trace(
        go.Scatter(
            x=timestamps,
            y=scales,
            line_width=2,
            text=scales,
            name="Scale (m)",
            marker_color="RoyalBlue",
            mode="lines",
        )
    )

    fig.update_xaxes(rangeslider_visible=True)

    for topic in topic_dict["status_topics"]:
        msgs = topic_msgs.get(topic)
        # msgs variable is an array or a NumPy array, and when you use it in the if statement,
        # Python is unable to determine its truth value because it has more than one element.
        if isinstance(msgs, np.ndarray):
            x, y = [], []
            for msg in msgs:
                timestamp = msg[0].astype("datetime64[ms]")
                x.extend([timestamp, timestamp, None])
                y.extend([0, scales.max(), None])

            fig.add_trace(
                go.Scatter(
                    x=x,
                    y=y,
                    line_width=3,
                    name=topic,
                    line_dash="dot",
                    legendgroup=topic,
                    connectgaps=False,
                )
            )

    fig.update_layout(
        xaxis_title="Time",
        yaxis_title="Scale (m)",
        showlegend=True,
        title={
            "text": title,
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
        },
    )

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def plot_processing_time(
    topic_msgs: dict[str, np.ndarray],
    topic_dict: dict[str, any],
    title: str,
    filename: str,
    show_plot: bool = False,
) -> None:
    """
    Plot the processing time topic over time.

    Args:
        topic_msgs (dict): Dictionary of topic messages.
        topic_dict (dict): Dictionary of topic names.
        title (str): Title of plot
        filename (str): Name of the output file.
        show_plot (bool, optional): Whether to show the plot.
    """

    # Create figure with secondary y-axis
    fig = make_subplots(
        rows=3,
        cols=1,
        subplot_titles=(
            "Queue Processing Time",
            "Optical Flow Processing Time",
            "Optimization Processing Time",
        ),
    )

    processing_time_topic = topic_msgs[topic_dict["processing_time_topic"]]

    timestamps = processing_time_topic[:, 0].astype("datetime64[ms]")
    queue_times = processing_time_topic[:, 1]
    optical_flow_times = processing_time_topic[:, 2]
    optimization_times = processing_time_topic[:, 3]

    # Queue processing time
    fig.add_trace(
        go.Scatter(
            x=timestamps,
            y=queue_times,
            line_width=2,
            text=queue_times,
            name="Queue time (ms)",
            marker_color="red",
            mode="lines",
        ),
        row=1,
        col=1,
    )

    # Optical flow processing time
    fig.add_trace(
        go.Scatter(
            x=timestamps,
            y=optical_flow_times,
            line_width=2,
            text=optical_flow_times,
            name="Optical Flow time (ms)",
            marker_color="green",
            mode="lines",
        ),
        row=2,
        col=1,
    )

    # Optimization processing time
    fig.add_trace(
        go.Scatter(
            x=timestamps,
            y=optimization_times,
            line_width=2,
            text=optimization_times,
            name="Optimization time (ms)",
            marker_color="blue",
            mode="lines",
        ),
        row=3,
        col=1,
    )

    # Update layout
    fig.update_layout(
        showlegend=True,
        title={
            "text": title,
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
        },
    )

    # Update y-axes labels
    fig.update_yaxes(title_text="Time (ms)", row=1, col=1)
    fig.update_yaxes(title_text="Time (ms)", row=2, col=1)
    fig.update_yaxes(title_text="Time (ms)", row=3, col=1)

    # Update x-axes labels
    fig.update_xaxes(title_text="Timestamps", row=3, col=1)

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def plot_altitude(
    topic_msgs: dict[str, np.ndarray],
    topic_dict: dict[str, any],
    title: str,
    filename: str,
    show_plot: bool = False,
) -> None:
    """
    Plot the processing time topic over time.

    Args:
        topic_msgs (dict): Dictionary of topic messages.
        topic_dict (dict): Dictionary of topic names.
        title (str): Title of plot
        filename (str): Name of the output file.
        show_plot (bool, optional): Whether to show the plot.
    """

    # Create figure with secondary y-axis
    fig = make_subplots(
        rows=4,
        cols=1,
        subplot_titles=(
            "Reference  Altitude",
            "Reference Altitude - Offset",
            "Estimated Altitude",
            "Metric Scale",
        ),
    )

    altitude_ref_topic = topic_msgs[topic_dict["altitude_ref_topic"]]
    timestamps_gt = altitude_ref_topic[:, 0].astype("datetime64[ms]")
    ref_altitude = altitude_ref_topic[:, 1]

    altitude_topic = topic_msgs[topic_dict["altitude_topic"]]
    timestamps = altitude_topic[:, 0].astype("datetime64[ms]")
    estimated_altitude = altitude_topic[:, 1]
    real_altitude_without_offset = altitude_topic[:, 2]
    metric_scale = altitude_topic[:, 3]

    fig.add_trace(
        go.Scatter(
            x=timestamps_gt,
            y=ref_altitude,
            line_width=2,
            text=ref_altitude,
            name="Reference Altitude",
            marker_color="gray",
            mode="lines+markers",  # Update mode to include both lines and markers
            marker=dict(size=4, symbol="circle", opacity=0.8),  # Set marker to circle
        ),
        row=1,
        col=1,
    )

    fig.add_trace(
        go.Scatter(
            x=timestamps,
            y=real_altitude_without_offset,
            line_width=2,
            text=real_altitude_without_offset,
            name="Reference Altitude - Offset",
            marker_color="red",
            mode="lines+markers",  # Update mode to include both lines and markers
            marker=dict(size=4, symbol="circle", opacity=0.8),  # Set marker to circle
        ),
        row=2,
        col=1,
    )

    fig.add_trace(
        go.Scatter(
            x=timestamps,
            y=estimated_altitude,
            line_width=2,
            text=estimated_altitude,
            name="Estimated Altitude",
            marker_color="green",
            mode="lines+markers",  # Update mode to include both lines and markers
            marker=dict(size=4, symbol="circle", opacity=0.8),  # Set marker to circle
        ),
        row=3,
        col=1,
    )

    fig.add_trace(
        go.Scatter(
            x=timestamps,
            y=metric_scale,
            line_width=2,
            text=metric_scale,
            name="Metric Scale",
            marker_color="blue",
            mode="lines+markers",  # Update mode to include both lines and markers
            marker=dict(size=4, symbol="circle", opacity=0.8),  # Set marker to circle
        ),
        row=4,
        col=1,
    )

    # Update layout
    fig.update_layout(
        showlegend=True,
        title={
            "text": title,
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
        },
        # hovermode="x unified",
        # Sync the x-axes of plots 2, 3, and 4
        xaxis2=dict(matches="x"),
        xaxis3=dict(matches="x"),
        xaxis4=dict(matches="x"),
    )

    fig.update_xaxes(matches="x")

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def plot_kitti_translation_error(
    translation_errors: np.ndarray,
    lengths: np.ndarray,
    title: str,
    filename: str,
    show_plot: bool = False,
) -> None:
    """Plot KITTI-style translation error metrics.

    Creates an interactive plot showing translation errors vs trajectory segment lengths
    using Plotly. The plot includes a line graph with markers showing how translational
    error varies with different trajectory lengths.

    Args:
        translation_errors (np.ndarray): Array of translation errors in percent for each length
        lengths (np.ndarray): Array of trajectory segment lengths in meters
        title (str): Title for the plot
        filename (str): Base filename to save plot (will save .html and .png)
        show_plot (bool, optional): Whether to display plot in browser. Defaults to False.

    Returns:
        None

    Notes:
        - Saves both interactive HTML (.html) and static image (.png) versions
        - Plot includes rangeslider for zooming/panning
        - X-axis shows trajectory lengths with custom tick labels
        - Y-axis shows translation error as a percentage
        - Points connected by lines with markers for better visualization
    """
    fig = go.Figure()

    fig.add_trace(
        go.Scatter(
            x=lengths,
            y=translation_errors,
            name="Translation Error (%)",
            marker=dict(
                color="RoyalBlue",
                size=4,
                symbol="circle",
                line=dict(color="RoyalBlue", width=2),
            ),
            mode="lines+markers+text",
        )
    )

    fig.update_xaxes(
        rangeslider_visible=True,
        tickmode="array",
        ticktext=[f"{x:.1f}" for x in lengths],  # Show all length values on x-axis
        tickvals=lengths,
    )

    fig.update_layout(
        xaxis_title="Length (m)",
        yaxis_title="Translation Error (%)",
        showlegend=True,
        title={
            "text": title,
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
        },
    )

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def plot_kitti_rotation_error(
    rotation_errors: np.ndarray,
    lengths: np.ndarray,
    title: str,
    filename: str,
    show_plot: bool = False,
) -> None:
    """Plot KITTI-style rotation error metrics.

    Creates an interactive plot showing rotation errors vs trajectory segment lengths
    using Plotly. The plot includes a line graph with markers showing how rotational
    error varies with different trajectory lengths.

    Args:
        rotation_errors (np.ndarray): Array of rotation errors in degrees/100m for each length
        lengths (np.ndarray): Array of trajectory segment lengths in meters
        title (str): Title for the plot
        filename (str): Base filename to save plot (will save .html and .png)
        show_plot (bool, optional): Whether to display plot in browser. Defaults to False.

    Returns:
        None

    Notes:
        - Saves both interactive HTML (.html) and static image (.png) versions
        - Plot includes rangeslider for zooming/panning
        - X-axis shows trajectory lengths with custom tick labels
        - Y-axis shows rotation error in degrees per 100m
        - Points connected by lines with markers for better visualization
    """
    fig = go.Figure()

    fig.add_trace(
        go.Scatter(
            x=lengths,
            y=rotation_errors,
            name="Rotational error (deg/100m)",
            marker=dict(
                color="RoyalBlue",
                size=4,
                symbol="circle",
                line=dict(color="RoyalBlue", width=2),
            ),
            mode="lines+markers+text",
        )
    )

    fig.update_xaxes(
        rangeslider_visible=True,
        tickmode="array",
        ticktext=[f"{x:.1f}" for x in lengths],  # Show all length values on x-axis
        tickvals=lengths,
    )

    fig.update_layout(
        xaxis_title="Length (m)",
        yaxis_title="Rotational error (deg/100m)",
        showlegend=True,
        title={
            "text": title,
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
        },
    )

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def plot_translation_drift_per_seconds(
    translation_errors: np.ndarray,
    timestamps: np.ndarray,
    title: str,
    filename: str,
    show_plot: bool = False,
) -> None:
    """
    Plot Quantum metric translation errors over time with statistical indicators.

    Args:
        translation_errors (np.ndarray): Array of translation errors in meters/second
        timestamps (np.ndarray): Array of timestamps in seconds
        title (str): Title of the plot
        filename (str): Base filename to save plot (will save .html and .png)
        show_plot (bool, optional): Whether to display plot in browser. Defaults to False.

    Returns:
        None

    Notes:
        - Saves both interactive HTML (.html) and static image (.png) versions
        - Plot includes mean, median, standard deviation indicators
        - X-axis shows time in seconds
        - Y-axis shows translation error in meters/second
        - Includes a range slider for interactive exploration
    """
    # Create figure and add main error line
    fig = go.Figure()

    # Calculate statistics
    mean = np.mean(translation_errors)
    std = np.std(translation_errors)
    median = np.median(translation_errors)
    rmse = np.sqrt(np.mean(np.square(translation_errors)))

    # Add main error trace
    fig.add_trace(
        go.Scatter(
            x=timestamps.astype("datetime64[s]"),
            y=translation_errors,
            line_color="gray",
            line_width=2,
            name="Translation Error",
        )
    )

    # Add standard deviation band
    fig.add_hrect(y0=mean - std / 2, y1=mean + std / 2, line_width=0, fillcolor="mediumpurple", opacity=0.2, name="std")

    # Add statistical lines
    fig.add_hline(y=mean, line_width=3, line_color="darkorange", name="mean")
    fig.add_hline(y=rmse, line_width=3, line_color="navy", name="rmse")
    fig.add_hline(y=median, line_width=3, line_color="gold", name="median")

    # Add legend entries for statistical indicators
    fig.add_trace(go.Scatter(x=[None], y=[None], name="std", line_color="mediumpurple"))
    fig.add_trace(go.Scatter(x=[None], y=[None], name="mean", line_color="darkorange"))
    fig.add_trace(go.Scatter(x=[None], y=[None], name="median", line_color="gold"))

    # Update layout
    fig.update_layout(
        xaxis_title="Time (s)",
        yaxis_title="Translation Error (m/s)",
        showlegend=True,
        title={
            "text": title,
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
            "font": {"size": 30},
        },
        width=1920,
        height=1080,
        font=dict(size=24),
    )

    # Add range slider
    # fig.update_xaxes(rangeslider_visible=True)

    # Save plots
    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")


def plot_velocity(
    topic_msgs: dict[str, np.ndarray],
    topic_dict: dict[str, any],
    title: str,
    filename: str,
    show_plot: bool = False,
) -> None:
    """
    Plot the velocity data showing linear and angular velocities over time.

    Args:
        topic_msgs (dict): Dictionary of topic messages.
        topic_dict (dict): Dictionary of topic names.
        title (str): Title of plot
        filename (str): Name of the output file.
        show_plot (bool, optional): Whether to show the plot.
    """
    # Create figure with 3x2 subplots
    fig = make_subplots(
        rows=3,
        cols=2,
        subplot_titles=(
            "Linear Velocity X",
            "Angular Velocity X",
            "Linear Velocity Y",
            "Angular Velocity Y",
            "Linear Velocity Z",
            "Angular Velocity Z",
        ),
        shared_xaxes=True,
    )

    velocity_topic = topic_msgs[topic_dict["velocity_topic"]]
    print("Velocity topic shape:", velocity_topic.shape)
    timestamps = velocity_topic[:, 0]

    # Linear velocities (x, y, z)
    linear_velocities = velocity_topic[:, 1:4]  # Columns 1,2,3
    angular_velocities = velocity_topic[:, 4:7]  # Columns 4,5,6

    colors = ["red", "green", "blue"]

    # Plot linear velocities (left column)
    for i in range(3):
        fig.add_trace(
            go.Scatter(
                x=timestamps.astype("datetime64[ms]"),
                y=linear_velocities[:, i],
                name=f"Linear Vel {'XYZ'[i]}",
                line_width=2,
                marker_color=colors[i],
                mode="lines",
            ),
            row=i + 1,
            col=1,
        )

    # Plot angular velocities (right column)
    for i in range(3):
        fig.add_trace(
            go.Scatter(
                x=timestamps.astype("datetime64[ms]"),
                y=angular_velocities[:, i],
                name=f"Angular Vel {'XYZ'[i]}",
                line_width=2,
                marker_color=colors[i],
                mode="lines",
            ),
            row=i + 1,
            col=2,
        )

    # Update y-axes labels
    for i in range(3):
        fig.update_yaxes(title_text="Velocity (m/s)", row=i + 1, col=1)
        fig.update_yaxes(title_text="Angular Velocity (rad/s)", row=i + 1, col=2)

    # Update x-axis label only for bottom plots
    fig.update_xaxes(title_text="Time", row=3, col=1)
    fig.update_xaxes(title_text="Time", row=3, col=2)

    # Update layout
    fig.update_layout(
        showlegend=True,
        title={
            "text": title,
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
        },
    )

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    # pio.write_image(fig, filename + ".png")


def plot_velocity_comparison(
    gt_timestamps: np.ndarray,
    gt_velocities: np.ndarray,
    est_timestamps: np.ndarray,
    est_velocities: np.ndarray,
    velocity_type: str,
    title: str,
    filename: str,
    show_plot: bool = False,
) -> None:
    """
    Plot velocity comparison between estimated and ground truth velocities.

    Args:
        gt_timestamps (np.ndarray): Ground truth timestamps.
        gt_velocities (np.ndarray): Ground truth velocity data (Nx3 array for x,y,z components).
        est_timestamps (np.ndarray): Estimated timestamps.
        est_velocities (np.ndarray): Estimated velocity data (Nx3 array for x,y,z components).
        velocity_type (str): Type of velocity to plot - "linear" or "angular".
        title (str): Title of plot
        filename (str): Name of the output file.
        show_plot (bool, optional): Whether to show the plot.
    """
    # Create figure with 3x1 subplots for X, Y, Z components
    fig = make_subplots(
        rows=3,
        cols=1,
        subplot_titles=(
            f"{velocity_type.capitalize()} Velocity X",
            f"{velocity_type.capitalize()} Velocity Y",
            f"{velocity_type.capitalize()} Velocity Z",
        ),
        shared_xaxes=True,
        vertical_spacing=0.08,
    )

    colors = ["red", "green", "blue"]

    # Determine velocity unit based on type
    if velocity_type.lower() == "linear":
        vel_unit = "(m/s)"
    elif velocity_type.lower() == "angular":
        vel_unit = "(rad/s)"
    else:
        raise ValueError("velocity_type must be 'linear' or 'angular'")

    # Convert timestamps to datetime format
    gt_timestamps_dt = gt_timestamps.astype("datetime64[ms]")
    est_timestamps_dt = est_timestamps.astype("datetime64[ms]")

    # Plot each velocity component (X, Y, Z)
    for i in range(3):
        axis_name = ["X", "Y", "Z"][i]

        # Add ground truth trace
        fig.add_trace(
            go.Scatter(
                x=gt_timestamps_dt,
                y=gt_velocities[:, i],
                name=f"GT {velocity_type.capitalize()} Vel {axis_name}",
                line_width=2,
                marker_color="gray",
                mode="lines",
            ),
            row=i + 1,
            col=1,
        )

        # Add estimated trace
        fig.add_trace(
            go.Scatter(
                x=est_timestamps_dt,
                y=est_velocities[:, i],
                name=f"Est {velocity_type.capitalize()} Vel {axis_name}",
                line_width=2,
                marker_color=colors[i],
                mode="lines",
            ),
            row=i + 1,
            col=1,
        )

        # Update y-axis label for each subplot
        fig.update_yaxes(title_text=f"Velocity {vel_unit}", row=i + 1, col=1)

    # Update x-axis label only for bottom plot
    fig.update_xaxes(title_text="Time", row=3, col=1)

    # Update layout
    fig.update_layout(
        showlegend=True,
        title={
            "text": title,
            "y": 0.98,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
        },
    )

    plot(fig, filename=filename + ".html", auto_open=show_plot)
    pio.write_image(fig, filename + ".png")
