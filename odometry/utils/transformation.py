# MIT License
# Copyright (c) 2018 Xiang<PERSON> Wang

# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:

# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.

# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

# Cridit: <PERSON><PERSON><PERSON> https://github.com/TimingSpace/Transformation

import numpy as np
import pymap3d as pm
import utm
from scipy.spatial.transform import Rotation as R


def line2mat(line_data):
    mat = np.eye(4)
    mat[0:3, :] = line_data.reshape(3, 4)
    return np.matrix(mat)


def motion2pose(data):
    data_size = len(data)
    all_pose = []  # np.zeros((data_size+1, 4, 4))
    all_pose.append(np.eye(4, 4))  # [0,:] = np.eye(4,4)
    pose = np.eye(4, 4)
    for i in range(0, data_size):
        pose = pose.dot(data[i])
        all_pose.append(pose)
    return all_pose


def pose2motion(data):
    data_size = len(data)
    all_motion = []
    for i in range(0, data_size - 1):
        motion = np.linalg.inv(data[i]).dot(data[i + 1])
        all_motion.append(motion)

    return np.array(all_motion)  # N x 4 x 4


def SE2se(SE_data):
    result = np.zeros(6)
    result[0:3] = np.array(SE_data[0:3, 3].T)
    result[3:6] = SO2so(SE_data[0:3, 0:3]).T
    return result


def SO2so(SO_data):
    return R.from_matrix(SO_data).as_rotvec()


def so2SO(so_data):
    return R.from_rotvec(so_data).as_matrix()


def se2SE(se_data):
    result_mat = np.matrix(np.eye(4))
    result_mat[0:3, 0:3] = so2SO(se_data[3:6])
    result_mat[0:3, 3] = np.matrix(se_data[0:3]).T
    return result_mat


# can get wrong result


def se_mean(se_datas):
    all_SE = np.matrix(np.eye(4))
    for i in range(se_datas.shape[0]):
        se = se_datas[i, :]
        SE = se2SE(se)
        all_SE = all_SE * SE
    all_se = SE2se(all_SE)
    mean_se = all_se / se_datas.shape[0]
    return mean_se


def ses_mean(se_datas):
    se_datas = np.array(se_datas)
    se_datas = np.transpose(
        se_datas.reshape(se_datas.shape[0], se_datas.shape[1], se_datas.shape[2] * se_datas.shape[3]), (0, 2, 1)
    )
    se_result = np.zeros((se_datas.shape[0], se_datas.shape[2]))
    for i in range(0, se_datas.shape[0]):
        mean_se = se_mean(se_datas[i, :, :])
        se_result[i, :] = mean_se
    return se_result


def ses2poses(data):
    data_size = data.shape[0]
    all_pose = np.zeros((data_size + 1, 12))
    temp = np.eye(4, 4).reshape(1, 16)
    all_pose[0, :] = temp[0, 0:12]
    pose = np.matrix(np.eye(4, 4))
    for i in range(0, data_size):
        data_mat = se2SE(data[i, :])
        pose = pose * data_mat
        pose_line = np.array(pose[0:3, :]).reshape(1, 12)
        all_pose[i + 1, :] = pose_line
    return all_pose


def SEs2ses(motion_data):
    data_size = motion_data.shape[0]
    ses = np.zeros((data_size, 6))
    for i in range(0, data_size):
        SE = np.matrix(np.eye(4))
        SE[0:3, :] = motion_data[i, :].reshape(3, 4)
        ses[i, :] = SE2se(SE)
    return ses


def so2quat(so_data):
    so_data = np.array(so_data)
    theta = np.sqrt(np.sum(so_data * so_data))
    axis = so_data / theta
    quat = np.zeros(4)
    quat[0:3] = np.sin(theta / 2) * axis
    quat[3] = np.cos(theta / 2)
    return quat


def quat2so(quat_data):
    quat_data = np.array(quat_data)
    sin_half_theta = np.sqrt(np.sum(quat_data[0:3] * quat_data[0:3]))
    axis = quat_data[0:3] / sin_half_theta
    cos_half_theta = quat_data[3]
    theta = 2 * np.arctan2(sin_half_theta, cos_half_theta)
    so = theta * axis
    return so


# input so_datas batch*channel*height*width
# return quat_datas batch*numner*channel


def sos2quats(so_datas, mean_std=[[1], [1]]):
    so_datas = np.array(so_datas)
    so_datas = so_datas.reshape(so_datas.shape[0], so_datas.shape[1], so_datas.shape[2] * so_datas.shape[3])
    so_datas = np.transpose(so_datas, (0, 2, 1))
    quat_datas = np.zeros((so_datas.shape[0], so_datas.shape[1], 4))
    for i_b in range(0, so_datas.shape[0]):
        for i_p in range(0, so_datas.shape[1]):
            so_data = so_datas[i_b, i_p, :]
            quat_data = so2quat(so_data)
            quat_datas[i_b, i_p, :] = quat_data
    return quat_datas


def SO2quat(SO_data):
    rr = R.from_matrix(SO_data)
    return rr.as_quat()


def quat2SO(quat_data):
    return R.from_quat(quat_data).as_matrix()


def pos_quat2SE(quat_data):
    SO = R.from_quat(quat_data[3:7]).as_matrix()
    SE = np.matrix(np.eye(4))
    SE[0:3, 0:3] = np.matrix(SO)
    SE[0:3, 3] = np.matrix(quat_data[0:3]).T
    SE = np.array(SE[0:3, :]).reshape(1, 12)
    return SE


def pos_quat2SE_matrix(quat_data):
    SO = R.from_quat(quat_data[3:7]).as_matrix()
    SE = np.matrix(np.eye(4))
    SE[0:3, 0:3] = np.matrix(SO)
    SE[0:3, 3] = np.matrix(quat_data[0:3]).T
    return SE


def pos_quats2SEs(quat_datas):
    data_len = quat_datas.shape[0]
    SEs = np.zeros((data_len, 12))
    for i_data in range(0, data_len):
        SE = pos_quat2SE(quat_datas[i_data, :])
        SEs[i_data, :] = SE
    return SEs


def pos_quats2SE_matrices(quat_datas):
    SEs = []
    for quat in quat_datas:
        SO = R.from_quat(quat[3:7]).as_matrix()
        SE = np.eye(4)
        SE[0:3, 0:3] = SO
        SE[0:3, 3] = quat[0:3]
        SEs.append(SE)
    return SEs


def SE2pos_quat(SE_data):
    pos_quat = np.zeros(7)
    pos_quat[3:] = SO2quat(SE_data[0:3, 0:3])
    pos_quat[:3] = SE_data[0:3, 3].T
    return pos_quat


def latlonhtoxyzwgs84(latitudes, longitudes, altitudes) -> np.ndarray:
    """
    Converts latitude, longitude and height to WGS84 cartesian coordinates
    Reference: https://stackoverflow.com/a/69604627

    :param latitudes: latitude in degrees
    :param longitudes: longitude in degrees
    :param altitudes: altitude in meters
    :return: x, y, z in meters
    """
    a: float = 6378137.0  # radius a of earth in meters cfr WGS84
    b: float = 6356752.3  # radius b of earth in meters cfr WGS84
    e2: float = 1 - (b**2 / a**2)
    latr: np.ndarray = np.radians(latitudes)  # latitude in radians
    lonr: np.ndarray = np.radians(longitudes)  # longitude in radians
    Nphi: np.ndarray = a / np.sqrt(1 - e2 * np.sin(latr) ** 2)
    x: np.ndarray = (Nphi + altitudes) * np.cos(latr) * np.cos(lonr)
    y: np.ndarray = (Nphi + altitudes) * np.cos(latr) * np.sin(lonr)
    z: np.ndarray = (b**2 / a**2 * Nphi + altitudes) * np.sin(latr)
    return np.column_stack([x, y, z])


def latlonhtoUTM(latitudes, longitudes, altitudes) -> np.ndarray:
    """
    Converts latitude, longitude and height to UTM cartesian coordinates
    Reference: https://stackoverflow.com/a/69604627

    :param latitudes: latitude in degrees
    :param longitudes: longitude in degrees
    :param altitudes: altitude in meters
    :return: x, y, z in meters
    """
    gt_coords = []
    for lat, lon, alt in zip(latitudes, longitudes, altitudes):
        gt_coords.append(np.array(utm.from_latlon(lat, lon)[:2]))
    gt_coords = np.stack(gt_coords)
    gt_coords = gt_coords - gt_coords.mean(axis=0)
    gt_coords = np.hstack([gt_coords, np.array(altitudes).reshape(-1, 1)])
    return gt_coords


def wgs84_geodetic_to_ecef(latitudes: np.ndarray, longitudes: np.ndarray, altitudes: np.ndarray) -> np.ndarray:
    """
    https://gis.stackexchange.com/a/261230

    Converts from geodetic coordinate system WGS84 (Latitude, Longitude, and Altitude) to
    ECEF (Earth-Centered, Earth-Fixed) cartesian coordinates system.

    Parameters:
        longitudes (np.ndarray): longitude values in degrees (-180 to 180).
        latitudes (np.ndarray): latitude values in degrees (-90 to 90).
        altitudes (np.ndarray): altitude values in meters.

    Returns:
        (np.ndarray) x, y, z ECEF Cartesian coordinates in meters.
    """

    x, y, z = pm.geodetic2ecef(latitudes, longitudes, altitudes)

    return np.column_stack([x, y, z])


def wgs84_geodetic_to_enu(latitudes: np.ndarray, longitudes: np.ndarray, altitudes: np.ndarray) -> np.ndarray:
    """
    Converts geographic coordinates from the WGS84 Geodetic system (Latitude, Longitude, and Altitude)
    to local East-North-Up (ENU) Cartesian coordinates. This conversion is useful for mapping or representing
    positions in a local topocentric reference frame where calculations and visualizations are often more
    intuitive. The ENU coordinates are centered at the first point in the input arrays, which acts as the
    local origin.

    The conversion process involves transforming WGS84 coordinates to the Earth-Centered, Earth-Fixed (ECEF)
    coordinate system, and then from ECEF to the East-North-Up (ENU) coordinate system. For more information,
    refer to the following resources:
    - https://en.wikipedia.org/wiki/Geographic_coordinate_conversion#From_ECEF_to_ENU
    - https://en.wikipedia.org/wiki/Local_tangent_plane_coordinates

    Parameters:
        latitudes (np.ndarray): An array of latitude values in degrees, where values range from -90 to 90.
        longitudes (np.ndarray): An array of longitude values in degrees, where values range from -180 to 180.
        altitudes (np.ndarray): An array of altitude values in meters above the WGS84 ellipsoid.

    Returns:
        np.ndarray: A 2D array where each row represents the local ENU coordinates (x, y, z) corresponding to
        the input geodetic coordinates. The ENU coordinates are expressed in meters, with 'x' for East, 'y'
        for North, and 'z' for Up components relative to the local origin.
    """

    x, y, z = pm.geodetic2enu(latitudes, longitudes, altitudes, lat0=latitudes[0], lon0=longitudes[0], h0=altitudes[0])

    return np.column_stack([x, y, z])


def wgs84_geodetic_to_ned(latitudes: np.ndarray, longitudes: np.ndarray, altitudes: np.ndarray) -> np.ndarray:
    """
    Converts geographic coordinates from the WGS84 Geodetic system (Latitude, Longitude, and Altitude)
    to local North-East-Down (NED) Cartesian coordinates. This conversion is useful for navigation and
    aerospace applications where the NED coordinate system is preferred. The NED coordinates are centered
    at the first point in the input arrays, which acts as the local origin.

    The conversion process involves transforming WGS84 coordinates to the Earth-Centered, Earth-Fixed (ECEF)
    coordinate system, and then from ECEF to the North-East-Down (NED) coordinate system. For more information,
    refer to the following resources:
    - https://en.wikipedia.org/wiki/Geographic_coordinate_conversion#From_ECEF_to_ENU
    - https://en.wikipedia.org/wiki/Local_tangent_plane_coordinates

    Parameters:
        latitudes (np.ndarray): An array of latitude values in degrees, where values range from -90 to 90.
        longitudes (np.ndarray): An array of longitude values in degrees, where values range from -180 to 180.
        altitudes (np.ndarray): An array of altitude values in meters above the WGS84 ellipsoid.

    Returns:
        np.ndarray: A 2D array where each row represents the local NED coordinates (x, y, z) corresponding to
        the input geodetic coordinates. The NED coordinates are expressed in meters, with 'x' for North, 'y'
        for East, and 'z' for Down components relative to the local origin.
    """

    n, e, d = pm.geodetic2ned(latitudes, longitudes, altitudes, lat0=latitudes[0], lon0=longitudes[0], h0=altitudes[0])

    return np.column_stack([n, e, d])
