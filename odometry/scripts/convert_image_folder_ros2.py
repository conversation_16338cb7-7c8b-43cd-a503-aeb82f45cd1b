import argparse
import os
import time
from pathlib import Path

import cv2
import numpy as np
from rosbags.rosbag2 import Writer
from rosbags.serde import serialize_cdr
from rosbags.typesys.types import sensor_msgs__msg__CompressedImage as CompressedImage
from rosbags.typesys.types import sensor_msgs__msg__Image as Image
from std_msgs.msg import Header


def parser():
    parser = argparse.ArgumentParser(description="Import images from a folder into a ROS2 bag file")
    parser.add_argument(
        "-i", "--input_folder", required=True, help="Input folder containing images (should have image_0 subfolder)"
    )
    parser.add_argument("-o", "--output_bag", required=True, help="Output ROS2 bag folder path")
    parser.add_argument(
        "-t", "--topic_name", default="/camera/image_raw/compressed", help="Image topic name in the bag file"
    )
    parser.add_argument("--fps", type=float, default=10.0, help="Frame rate to use for timestamps (default: 10 fps)")
    return parser.parse_args()


def get_sorted_images(image_folder: str) -> list:
    """Get sorted list of image files from the folder."""
    image_files = []
    for ext in [".png", ".jpg", ".jpeg"]:
        image_files.extend(Path(image_folder).glob(f"*{ext}"))
    return sorted(image_files)


def create_image_msg(cv_image: np.ndarray, timestamp_ns: int, filename: str, frame_id: str = "camera") -> Image:
    """Create a ROS Image message from a CV image."""

    sec = int(timestamp_ns // 1e9)
    nanosec = int(timestamp_ns % 1e9)

    header = Header()
    header.frame_id = frame_id
    header.stamp.sec = sec
    header.stamp.nanosec = nanosec

    # Convert image to uint8 if not already
    if cv_image.dtype != np.uint8:
        cv_image = cv_image.astype(np.uint8)

    message = CompressedImage(
        header=header,
        format="png",
        data=np.fromfile(filename, dtype=np.uint8),
    )

    return message


def write_images_to_bag(args):
    """Write images from folder to ROS2 bag file."""

    # Setup image folder path
    image_folder = os.path.join(args.input_folder, "image_0")
    if not os.path.exists(image_folder):
        raise FileNotFoundError(f"Image folder not found: {image_folder}")
    print("images loaded")

    # Get image files
    image_files = get_sorted_images(image_folder)
    if not image_files:
        raise FileNotFoundError(f"No images found in {image_folder}")
    print("images found")

    time_step_ns = int(1e9 / args.fps)  # Convert to nanoseconds

    # Get current time for the first frame
    start_time = time.time_ns()

    # Generate timestamps for all frames
    num_images = len(image_files)
    timestamps = [start_time + i * time_step_ns for i in range(num_images)]
    with Writer(args.output_bag) as writer:
        connection = writer.add_connection(args.topic_name, CompressedImage.__msgtype__)

        for img_file, timestamp in zip(image_files, timestamps):
            cv_image = cv2.imread(str(img_file))

            if cv_image is None:
                print(f"Failed to read image: {img_file}")
                continue

            try:
                img_msg = create_image_msg(cv_image, timestamp, img_file)

                # Serialize and write message
                serialized_msg = serialize_cdr(img_msg, connection.msgtype)
                writer.write(connection, timestamp, serialized_msg)

                print(f"Wrote image {img_file.name}")
            except Exception as e:
                print(f"Error processing image {img_file}: {str(e)}")
                continue


def main():
    args = parser()
    try:
        write_images_to_bag(args)
        print(f"Successfully created ROS2 bag at: {args.output_bag}")
    except Exception as e:
        print(f"Error: {str(e)}")
        return 1
    return 0


if __name__ == "__main__":
    main()
