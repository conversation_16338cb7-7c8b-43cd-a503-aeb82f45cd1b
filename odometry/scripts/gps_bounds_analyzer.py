#!/usr/bin/env python3
"""
GPS Bounds Analyzer
Finds the most southwest and northeast positions from GPS trajectory data.
"""

import argparse
import os
from pathlib import Path

import numpy as np
import pandas as pd


def find_gps_bounds(csv_file):
    """
    Find the most southwest and northeast positions from GPS data.

    Args:
        csv_file (str): Path to the CSV file containing GPS data

    Returns:
        dict: Dictionary containing southwest and northeast bounds
    """
    # Read the CSV file
    df = pd.read_csv(csv_file)

    # Ensure required columns exist
    required_columns = ["timestamp", "latitude", "longitude"]
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")

    # Find extreme positions
    min_lat = df["latitude"].min()
    max_lat = df["latitude"].max()
    min_lon = df["longitude"].min()
    max_lon = df["longitude"].max()

    # Southwest position (minimum latitude and longitude)
    southwest = {
        "latitude": min_lat,
        "longitude": min_lon,
        "timestamp": df.loc[df["latitude"] == min_lat, "timestamp"].iloc[0]
        if len(df[df["latitude"] == min_lat]) > 0
        else None,
    }

    # Northeast position (maximum latitude and longitude)
    northeast = {
        "latitude": max_lat,
        "longitude": max_lon,
        "timestamp": df.loc[df["latitude"] == max_lat, "timestamp"].iloc[0]
        if len(df[df["latitude"] == max_lat]) > 0
        else None,
    }

    # Calculate bounding box dimensions
    lat_span = max_lat - min_lat
    lon_span = max_lon - min_lon

    # Calculate approximate distances (rough estimation)
    # 1 degree of latitude ≈ 111 km
    # 1 degree of longitude ≈ 111 km * cos(latitude)
    avg_lat = (min_lat + max_lat) / 2
    lat_distance_km = lat_span * 111
    lon_distance_km = lon_span * 111 * np.cos(np.radians(avg_lat))

    bounds = {
        "southwest": southwest,
        "northeast": northeast,
        "bounds": {
            "min_latitude": min_lat,
            "max_latitude": max_lat,
            "min_longitude": min_lon,
            "max_longitude": max_lon,
        },
        "dimensions": {
            "latitude_span_degrees": lat_span,
            "longitude_span_degrees": lon_span,
            "latitude_span_km": lat_distance_km,
            "longitude_span_km": lon_distance_km,
        },
        "total_points": len(df),
    }

    return bounds


def print_bounds(bounds, filename):
    """Print the bounds in a formatted way."""
    print(f"\n=== GPS Bounds Analysis for {filename} ===")
    print(f"Total GPS points: {bounds['total_points']:,}")

    print(f"\n📍 Most Southwest Position:")
    print(f"   Latitude:  {bounds['southwest']['latitude']:.8f}°")
    print(f"   Longitude: {bounds['southwest']['longitude']:.8f}°")
    if bounds["southwest"]["timestamp"]:
        print(f"   Timestamp: {bounds['southwest']['timestamp']}")

    print(f"\n📍 Most Northeast Position:")
    print(f"   Latitude:  {bounds['northeast']['latitude']:.8f}°")
    print(f"   Longitude: {bounds['northeast']['longitude']:.8f}°")
    if bounds["northeast"]["timestamp"]:
        print(f"   Timestamp: {bounds['northeast']['timestamp']}")

    print(f"\n📐 Bounding Box:")
    print(f"   Latitude range:  {bounds['bounds']['min_latitude']:.8f}° to {bounds['bounds']['max_latitude']:.8f}°")
    print(f"   Longitude range: {bounds['bounds']['min_longitude']:.8f}° to {bounds['bounds']['max_longitude']:.8f}°")
    print(
        f"   Span: {bounds['dimensions']['latitude_span_degrees']:.6f}° lat × {bounds['dimensions']['longitude_span_degrees']:.6f}° lon"
    )
    print(
        f"   Approximate size: {bounds['dimensions']['latitude_span_km']:.2f} km × {bounds['dimensions']['longitude_span_km']:.2f} km"
    )


def main():
    parser = argparse.ArgumentParser(description="Find GPS bounds from trajectory data")
    parser.add_argument("csv_file", help="Path to the CSV file containing GPS data")
    parser.add_argument("--output", "-o", help="Output file for results (optional)")

    args = parser.parse_args()

    if not os.path.exists(args.csv_file):
        print(f"Error: File '{args.csv_file}' not found.")
        return

    try:
        bounds = find_gps_bounds(args.csv_file)
        print_bounds(bounds, os.path.basename(args.csv_file))

        # Save results to file if requested
        if args.output:
            import json

            with open(args.output, "w") as f:
                json.dump(bounds, f, indent=2)
            print(f"\nResults saved to: {args.output}")

    except Exception as e:
        print(f"Error processing file: {e}")


if __name__ == "__main__":
    main()
