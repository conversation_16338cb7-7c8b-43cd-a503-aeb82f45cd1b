import argparse
import logging.config
import os

import cv2
import numpy as np
from cv_bridge import CvBridge
from rosbags.rosbag2 import Reader
from rosbags.typesys import Stores, get_typestore

logging.config.fileConfig("../logging.conf")
logger = logging.getLogger(__name__)


def parser():
    parser = argparse.ArgumentParser(
        description="Extract image topic from a ros2 bag file and store them into a folder"
    )
    parser.add_argument("-n", "--topic_names", nargs="+", default=[])
    parser.add_argument("-i", "--input_bag_file", default="", help="Path of ros2 bag file")
    parser.add_argument("-o", "--output_folder_path", default="./temp", help="Output folder path")
    parser.add_argument("-c", "--calibration_file", default="", help="camera calibration file")
    parser.add_argument("--start_offset", type=int, default=0, help="Number of images to skip from start")
    parser.add_argument(
        "--apply_filter", action="store_true", default=False, help="Apply filtering to the image."
    )  # New filter parameter
    parser.add_argument("--scale", type=float, default=1.0, help="Image scaling factor.")  # New scale parameter
    parser.add_argument("--clahe_clip", type=float, default=2.0, help="CLAHE clip limit")
    parser.add_argument("--clahe_grid", type=int, default=8, help="CLAHE grid size")
    parser.add_argument("--bilateral_d", type=int, default=9, help="Bilateral filter diameter")
    parser.add_argument("--bilateral_sigma_color", type=float, default=75, help="Bilateral filter sigma color")
    parser.add_argument("--bilateral_sigma_space", type=float, default=75, help="Bilateral filter sigma space")

    return parser.parse_args()


def distort_images(calibration_file):
    calib_params = np.loadtxt(calibration_file)
    K = np.eye(3)
    K[0, 0] = calib_params[0]
    K[1, 1] = calib_params[1]
    K[0, 2] = calib_params[2]
    K[1, 2] = calib_params[3]
    distort_params = calib_params[4:]
    return K, distort_params


def extract_topics(args):
    """Extract some topics, from a ros2 bag file."""
    bridge = CvBridge()
    cnt = 0
    skip_count = 0
    typestore = get_typestore(Stores.ROS2_HUMBLE)

    if args.calibration_file:
        K, distort_params = distort_images(args.calibration_file)

    with Reader(args.input_bag_file) as reader:
        for connection, timestamp, rawdata in reader.messages():
            if connection.topic == args.topic_names[0]:
                # Skip images based on start_offset
                if skip_count < args.start_offset:
                    skip_count += 1
                    continue

                msg = typestore.deserialize_cdr(rawdata, connection.msgtype)
                if connection.msgtype == "sensor_msgs/msg/Image":
                    cv_image = bridge.imgmsg_to_cv2(msg)
                elif connection.msgtype == "sensor_msgs/msg/CompressedImage":
                    cv_image = bridge.compressed_imgmsg_to_cv2(msg)
                else:
                    print(f"Unsupported message type: {connection.msgtype}")
                    continue

                # Convert to grayscale if not already
                if len(cv_image.shape) > 2:
                    cv_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)

                if args.calibration_file:
                    cv_image = cv2.undistort(cv_image, K, distort_params)

                if args.scale != 1.0:
                    # Resize the image
                    width = int(cv_image.shape[1] * args.scale)
                    height = int(cv_image.shape[0] * args.scale)
                    cv_image = cv2.resize(cv_image, (width, height), interpolation=cv2.INTER_AREA)

                timestamp = "{}.{}".format(msg.header.stamp.sec, msg.header.stamp.nanosec)
                output_folder = os.path.join(args.output_folder_path, "image_0")
                filename = os.path.join(output_folder, "{:06d}.png".format(cnt))
                cv2.imwrite(filename, cv_image)
                print(f"Grayscale image with timestamp: {timestamp} wrote in {filename}")
                cnt += 1

        assumed_fps = 10
        stamps = np.linspace(0, assumed_fps * cnt, cnt)
        output_file = os.path.join(args.output_folder_path, "times.txt")
        np.savetxt(output_file, stamps, fmt="%.6e")


def prepare_data(args):
    if not os.path.exists(os.path.join(args.output_folder_path, "image_0")):
        os.makedirs(os.path.join(args.output_folder_path, "image_0"))


def main():
    args = parser()
    # logger.info(args.topic_names)
    prepare_data(args)
    extract_topics(args)


if __name__ == "__main__":
    main()
