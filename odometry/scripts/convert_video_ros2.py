import argparse
import time

import cv2
import numpy as np
from rosbags.rosbag2 import Writer
from rosbags.serde import serialize_cdr
from rosbags.typesys.types import sensor_msgs__msg__CompressedImage as CompressedImage
from std_msgs.msg import Header


def parser():
    parser = argparse.ArgumentParser(description="Convert video file to ROS2 bag with compressed images")
    parser.add_argument("-i", "--input_video", required=True, help="Input video file path (mp4 or mkv)")
    parser.add_argument("-o", "--output_bag", required=True, help="Output ROS2 bag folder path")
    parser.add_argument(
        "-t", "--topic_name", default="/camera/image_raw/compressed", help="Compressed image topic name in the bag file"
    )
    parser.add_argument(
        "--target_fps",
        type=float,
        default=None,
        help="Target FPS for the output bag (default: use video's original FPS)",
    )
    parser.add_argument(
        "--format", choices=["jpg", "png"], default="jpg", help="Compression format for images (default: jpg)"
    )
    parser.add_argument(
        "--quality", type=int, default=95, help="Compression quality for JPEG format (0-100, default: 95)"
    )
    return parser.parse_args()


def create_compressed_msg(
    frame: np.ndarray, timestamp_ns: int, format: str, quality: int, frame_id: str = "camera"
) -> CompressedImage:
    """Create a ROS CompressedImage message from a video frame."""
    sec = int(timestamp_ns // 1e9)
    nanosec = int(timestamp_ns % 1e9)

    header = Header()
    header.frame_id = frame_id
    header.stamp.sec = sec
    header.stamp.nanosec = nanosec

    # Encode image based on format
    if format == "jpg":
        encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), quality]
        _, img_encoded = cv2.imencode(".jpg", frame, encode_param)
    else:  # png
        _, img_encoded = cv2.imencode(".png", frame)

    # Create CompressedImage message
    message = CompressedImage(header=header, format=format, data=img_encoded.tobytes())

    return message


def write_video_to_bag(args):
    """Write video frames to ROS2 bag file as compressed images."""
    video = cv2.VideoCapture(args.input_video)
    if not video.isOpened():
        raise ValueError(f"Could not open video file: {args.input_video}")

    original_fps = video.get(cv2.CAP_PROP_FPS)
    frame_count = int(video.get(cv2.CAP_PROP_FRAME_COUNT))

    # Use target FPS if specified, otherwise use video's original FPS
    fps = args.target_fps if args.target_fps is not None else original_fps

    # Calculate time step between frames
    time_step_ns = int(1e9 / fps)  # Convert to nanoseconds

    print(f"Processing video with {frame_count} frames at {fps} FPS")
    print(f"Original video FPS: {original_fps}")
    start_time = time.time_ns()

    try:
        with Writer(args.output_bag) as writer:
            connection = writer.add_connection(args.topic_name, CompressedImage.__msgtype__)

            frame_number = 0
            while True:
                ret, frame = video.read()
                if not ret:
                    break

                # Skip frames if target FPS is lower than original
                if args.target_fps is not None and args.target_fps < original_fps:
                    if frame_number % int(original_fps / args.target_fps) != 0:
                        frame_number += 1
                        continue

                # Calculate timestamp for current frame
                timestamp = start_time + frame_number * time_step_ns

                try:
                    img_msg = create_compressed_msg(frame, timestamp, args.format, args.quality)

                    serialized_msg = serialize_cdr(img_msg, connection.msgtype)
                    writer.write(connection, timestamp, serialized_msg)

                    if frame_number % 100 == 0:
                        print(f"Processed frame {frame_number}/{frame_count}")

                except Exception as e:
                    print(f"Error processing frame {frame_number}: {str(e)}")
                    continue

                frame_number += 1

            print(f"Successfully processed {frame_number} frames")

    finally:
        video.release()


def main():
    args = parser()
    try:
        write_video_to_bag(args)
        print(f"Successfully created ROS2 bag at: {args.output_bag}")
    except Exception as e:
        print(f"Error: {str(e)}")
        return 1
    return 0


if __name__ == "__main__":
    main()
