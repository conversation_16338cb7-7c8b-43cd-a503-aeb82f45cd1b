#!/usr/bin/env python3
"""
VIO Experiment Pipeline

This module manages ROS2 bag recording, Docker container orchestration, and evaluation
for Visual-Inertial Odometry (VIO) experiments with GNSS integration.
Author: <PERSON><PERSON>
"""

import json
import logging
import os
import re
import shutil
import signal
import subprocess
import threading
import time
from pathlib import Path

import plotly.graph_objects as go
import rclpy
import yaml
from aim import Run
from bs4 import BeautifulSoup
from rclpy.node import Node
from rclpy.qos import QoSHistoryPolicy, QoSProfile, QoSReliabilityPolicy
from sensor_msgs.msg import Image
from tqdm import tqdm

# Set up logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class ProcessManager:
    """
    Utility class for managing subprocess execution and logging.

    This class provides static methods to execute shell commands and handle their output logging
    in a consistent manner. It supports both direct command execution and logging to files.
    """

    @staticmethod
    def run_command(
        command: str, cwd: str = None, output_dir: str = None, log_prefix: str = None
    ) -> subprocess.CompletedProcess:
        """
        Execute a shell command with optional output logging.

        Args:
            command: Shell command to execute
            cwd: Working directory for command execution
            output_dir: Directory to store log files
            log_prefix: Prefix for log file names

        Returns:
            CompletedProcess: Instance containing execution results
            None: If execution fails

        Raises:
            subprocess.CalledProcessError: If command execution fails
        """
        try:
            if output_dir and log_prefix:
                log_file = os.path.join(output_dir, f"{log_prefix}.log")

                with open(log_file, "w") as f:
                    result = subprocess.run(
                        command,
                        shell=True,
                        cwd=cwd,
                        stdout=f,
                        stderr=subprocess.STDOUT,
                        text=True,
                    )
                logger.info(f"Command '{command}' output logged to: {log_file}")
                return result
            else:
                result = subprocess.run(
                    command,
                    shell=True,
                    cwd=cwd,
                    check=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                )
                logger.info(f"Command executed: {command}")
                logger.info("Output:\n%s", result.stdout)
                if result.stderr:
                    logger.info("Errors/Warnings:\n%s", result.stderr)
                return result

        except subprocess.CalledProcessError as e:
            logger.error(f"Error executing command: {command}")
            logger.error("Error output:\n%s", e.stderr)
            return None


class FileSystemManager:
    """
    Utility class for handling file system operations.

    Provides methods for directory management, disk space checking, and output directory setup.
    Includes safety checks and user confirmations for critical operations.
    """

    @staticmethod
    def ensure_directory(path: str) -> bool:
        """
        Create directory if it doesn't exist.

        Args:
            path: Directory path to create

        Returns:
            bool: True if directory exists or was created successfully, False otherwise

        Raises:
            OSError: If directory creation fails
        """
        try:
            os.makedirs(path, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"Error creating directory {path}: {e}")
            return False

    @staticmethod
    def check_disk_space(path: str, required_gb: int = 10) -> bool:
        """
        Check if there's enough disk space available at the specified path.

        Args:
            path: Path to check disk space
            required_gb: Required space in gigabytes

        Returns:
            bool: True if enough space is available, False otherwise
        """
        total, used, free = shutil.disk_usage(path)
        free_gb = free // (2**30)  # Convert to GB
        if free_gb < required_gb:
            logger.warning(f"Low disk space! Only {free_gb} GB available")
            return False
        return True

    @staticmethod
    def setup_output_directory(output_path: str) -> bool:
        """
        Check and setup the output directory with user confirmation if it doesn't exist.

        Args:
            output_path: Path to the output directory

        Returns:
            bool: True if directory is ready to use, False otherwise

        Note:
            Prompts user for confirmation before creating new directory
        """
        if os.path.exists(output_path):
            logger.info(f"Output directory already exists: {output_path}")
            return True

        logger.warning(f"Output directory does not exist: {output_path}")
        while True:
            response = input("Would you like to create this directory? [y/n]: ").lower()
            if response in ["y", "yes"]:
                try:
                    os.makedirs(output_path)
                    logger.info(f"Created output directory: {output_path}")
                    return True
                except Exception as e:
                    logger.error(f"Failed to create output directory: {e}")
                    return False
            elif response in ["n", "no"]:
                logger.error("Cannot proceed without output directory")
                return False
            else:
                print("Please answer 'y' or 'n'")


class MessageCounter(Node):
    """
    ROS2 node for counting messages on a topic.

    Subscribes to a specified topic and maintains a count of received messages.
    Useful for monitoring data flow and verifying recording completeness.
    """

    def __init__(self, topic_name: str):
        """
        Initialize the message counter node.

        Args:
            topic_name: Name of the ROS2 topic to monitor
        """
        super().__init__("message_counter")

        qos = QoSProfile(
            reliability=QoSReliabilityPolicy.BEST_EFFORT,
            history=QoSHistoryPolicy.KEEP_LAST,
            depth=10,
        )

        self.count = 0
        self.subscription = self.create_subscription(
            Image,
            topic_name,
            self.callback,
            qos_profile=qos,
        )

    def callback(self, msg):
        """
        Callback function for message subscription.

        Args:
            msg: Received message from the subscribed topic

        Note:
            Increments internal message counter on each received message
        """
        self.count += 1


class BagRecordingManager:
    """
    Manages ROS2 bag recording and related Docker processes.

    Handles starting and stopping bag recordings, verifying recordings,
    and managing associated Docker containers for VIO experiments.
    """

    def __init__(self, config: dict):
        """
        Initialize the BagRecordingManager.

        Args:
            config: Dictionary containing configuration parameters including paths
                   to granite_ros and gnss_sync repositories
        """
        self.record_process = None
        self.docker_processes = []
        self.total_messages = 0
        self.granite_ros_path = config["granite_ros_path"]
        self.spl_gnss_denied_path = config["gnss_sync_path"]
        self.recorded_topics = config.get("recorded_topics", [])
        self.process_manager = ProcessManager()

    def start_record(self, sequence_num: int, output_path: str, base_name: str) -> bool:
        """
        Start recording ROS2 bag with specified sequence number.

        Args:
            sequence_num: Sequence number for the bag file
            output_path: Base path for output directory
            base_name: Base name for the output bag file

        Returns:
            bool: True if recording started successfully
        """
        output_name = f"{base_name}_{sequence_num:02d}"
        output_dir = os.path.join(output_path, output_name)

        # Check and remove existing directory and bag files
        if os.path.exists(output_dir):
            logger.info(f"Removing existing directory: {output_dir}")
            shutil.rmtree(output_dir)

        # Also check for any stray bag files in the output_path
        existing_bag = os.path.join(output_path, output_name)
        if os.path.exists(existing_bag):
            logger.info(f"Removing existing bag: {existing_bag}")
            shutil.rmtree(existing_bag)

        # Ensure parent directory exists
        os.makedirs(output_path, exist_ok=True)

        topics = " ".join(self.recorded_topics)

        # Change working directory to output_path before recording
        command = f"cd {output_path} && ros2 bag record -s mcap -o {output_name} {topics}"

        try:
            log_file = os.path.join(output_path, f"{output_name}_record.log")
            with open(log_file, "w") as f:
                self.record_process = subprocess.Popen(command, shell=True, stdout=f, stderr=f, text=True)
            logger.info(f"Started recording bag {output_name}")
            # Give it a moment to start recording
            time.sleep(1)
            return True
        except Exception as e:
            logger.error(f"Error starting recording: {e}")
            return False

    def verify_bag_recording(self, output_path: str, iteration: int, base_name: str) -> bool:
        """
        Verify that the bag recording completed successfully.

        Args:
            output_path: Path to the output directory
            iteration: Sequence number of the bag file
            base_name: Base name for the bag file

        Returns:
            bool: True if verification passed, False otherwise

        Note:
            Checks for metadata.yaml and runs ros2 bag info command
        """
        bag_name = f"{base_name}_{iteration:02d}"
        bag_path = os.path.join(output_path, bag_name)

        # Check if metadata file exists
        metadata_file = os.path.join(bag_path, "metadata.yaml")
        if not os.path.exists(metadata_file):
            logger.error(f"Bag recording failed - metadata.yaml not found in {bag_path}")
            return False

        try:
            result = subprocess.run(
                f"ros2 bag info {bag_path}",
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=10,
            )
            if result.returncode != 0:
                logger.error(f"Bag verification failed - {result.stderr}")
                return False
            return True
        except subprocess.TimeoutExpired:
            logger.error("Bag verification timed out")
            return False
        except Exception as e:
            logger.error(f"Error verifying bag: {e}")
            return False

    def stop_record(self):
        """Stop the current recording process."""
        if self.record_process:
            try:
                logger.info("Sending SIGINT to recording process...")
                self.record_process.send_signal(signal.SIGINT)

                try:
                    logger.info("Waiting for recording process to finish...")
                    self.record_process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    logger.warning("Recording process didn't stop gracefully, forcing termination...")
                    self.record_process.kill()
                    self.record_process.wait()

                logger.info("Cleaning up any remaining bag record processes...")
                subprocess.run(
                    "pkill -f 'ros2 bag record'",
                    shell=True,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                )

                logger.info("Waiting for filesystem to sync...")
                time.sleep(2)

            except Exception as e:
                logger.error(f"Error stopping recording process: {e}")
            finally:
                self.record_process = None

    def cleanup(self):
        """Clean up all processes."""
        self.stop_record()
        logger.info("Stopping Docker containers...")
        self.process_manager.run_command("make stop", cwd=self.granite_ros_path)
        self.process_manager.run_command("make stop", cwd=self.spl_gnss_denied_path)

    def get_topic_count(self, bag_path: str, topic_name: str = "/boson_cam/image_raw") -> int:
        """
        Get the count of messages for a specific topic in a bag file.

        Args:
            bag_path: Path to the bag file
            topic_name: Name of the topic to count messages for

        Returns:
            int: Number of messages for the specified topic
        """
        command = f"ros2 bag info {bag_path} | grep '{topic_name}'"
        result = self.process_manager.run_command(command)
        if result and result.stdout:
            try:
                parts = result.stdout.split("|")
                for part in parts:
                    if "Count:" in part:
                        count = int(part.split("Count:")[1].strip())
                        return count
                logger.warning(f"Could not find Count field in output: {result.stdout}")
            except (IndexError, ValueError) as e:
                logger.error(f"Error parsing message count for topic {topic_name}: {e}")
        return 0


class ConfigManager:
    """
    Manages VIO configuration updates and validation.

    Provides utilities for reading, updating, and validating configuration parameters
    in nested dictionary structures, with support for dot notation access.
    """

    @staticmethod
    def get_nested_value(config: dict, param_path: str) -> tuple[bool, any]:
        """
        Get value from nested dictionary using dot notation path.

        Args:
            config: Configuration dictionary
            param_path: Parameter path in dot notation (e.g., "value0.config.vio_max_kfs")

        Returns:
            tuple: (success, value) where success is bool and value is the retrieved value
                  Returns (False, None) if path doesn't exist
        """
        try:
            parts = param_path.split(".")
            current = config

            # Handle the special case of value0.config.* structure
            if parts[0] == "value0" and parts[1] == "config":
                key = ".".join(parts[1:])  # Join back the rest with dots
                if key in current["value0"]:
                    return True, current["value0"][key]
            return False, None
        except (KeyError, TypeError):
            return False, None

    @staticmethod
    def set_nested_value(config: dict, param_path: str, value: any) -> bool:
        """
        Set value in nested dictionary using dot notation path.

        Args:
            config: Configuration dictionary
            param_path: Parameter path in dot notation
            value: Value to set

        Returns:
            bool: True if successful
        """
        try:
            parts = param_path.split(".")
            if parts[0] == "value0" and parts[1] == "config":
                key = ".".join(parts[1:])  # Join back the rest with dots
                if key in config["value0"]:
                    config["value0"][key] = value
                    return True
            return False
        except (KeyError, TypeError):
            return False

    @staticmethod
    def update_vio_param(config_path: str, param_path: str, value: any) -> bool:
        """
        Update a parameter in the VIO configuration file.

        Args:
            config_path: Path to the configuration file
            param_path: Path to parameter in config (dot notation)
            value: New value for parameter

        Returns:
            bool: True if update was successful
        """
        try:
            with open(config_path, "r") as f:
                config = json.load(f)

            # First verify parameter exists
            exists, _ = ConfigManager.get_nested_value(config, param_path)
            if not exists:
                logger.error(f"Parameter {param_path} not found in config file")
                return False

            # Update parameter
            if not ConfigManager.set_nested_value(config, param_path, value):
                logger.error(f"Failed to update parameter {param_path}")
                return False

            with open(config_path, "w") as f:
                json.dump(config, f, indent=2)

            logger.info(f"Updated {param_path} to {value}")
            return True
        except Exception as e:
            logger.error(f"Error updating config file: {e}")
            return False

    @staticmethod
    def verify_param_exists(config_path: str, param_name: str) -> bool:
        """
        Verify that a parameter exists in the VIO configuration file.

        Args:
            config_path: Path to the configuration file
            param_name: Path to parameter in config (dot notation)

        Returns:
            bool: True if parameter exists
        """
        try:
            with open(config_path, "r") as f:
                config = json.load(f)

            exists, _ = ConfigManager.get_nested_value(config, param_name)
            if not exists:
                logger.error(f"Parameter {param_name} not found in config file")
            return exists
        except Exception as e:
            logger.error(f"Error reading config file: {e}")
            return False

    @staticmethod
    def print_config_structure(config_path: str, param_name: str):
        """
        Print the current value of the specified parameter.

        Args:
            config_path: Path to the configuration file
            param_name: Name of the parameter to print
        """
        try:
            with open(config_path, "r") as f:
                config = json.load(f)

            # Get the current value
            exists, value = ConfigManager.get_nested_value(config, param_name)
            if exists:
                logger.info(f"Current value of {param_name}: {value}")
            else:
                logger.error(f"Parameter {param_name} not found in config")
                if "value0" in config and isinstance(config["value0"], dict):
                    logger.info("Available parameters in value0:")
                    for key in config["value0"].keys():
                        logger.info(f"  - {key}")

        except Exception as e:
            logger.error(f"Error reading config file: {e}")


class ExperimentRunner:
    """
    Manages the execution of VIO experiments.

    Coordinates the execution of experiments including ROS2 bag playback,
    message counting, and process management. Handles setup, monitoring,
    and cleanup of experiment processes.
    """

    def __init__(self, config_path: str, base_output_path: str, experiment_config: dict):
        """
        Initialize the experiment runner.

        Args:
            config_path: Path to the VIO configuration file
            base_output_path: Base path for storing experiment results
            experiment_config: Dictionary containing experiment configuration parameters
        """
        self.config_path = config_path
        self.base_output_path = base_output_path
        self.bag_path = experiment_config["input_bag_path"]
        self.monitor_topic = experiment_config["monitor_topic"]
        self.gnss_cutoff_time = experiment_config["gnss_cutoff_time"]
        self.experiment_config = experiment_config
        self.manager = None
        self.counter_node = None
        self.ros_thread = None

    def setup_ros_counter(self) -> bool:
        """
        Set up ROS2 message counter node.

        Returns:
            bool: True if setup was successful
        """
        try:
            rclpy.init()
            self.counter_node = MessageCounter(self.monitor_topic)
            self.ros_thread = threading.Thread(target=self._spin_ros, daemon=True)
            self.ros_thread.start()
            return True
        except Exception as e:
            logger.error(f"Error setting up ROS counter: {e}")
            return False

    def _spin_ros(self):
        """ROS2 spin function for the counter node."""
        rclpy.spin(self.counter_node)

    def run_single_iteration(self, max_kfs: int, iteration: int, output_path: str, base_name: str) -> bool:
        """
        Run a single iteration of the experiment.

        Args:
            max_kfs: Value of max_kfs parameter
            iteration: Current iteration number
            output_path: Path for storing results
            base_name: Base name for the output bag file

        Returns:
            bool: True if iteration completed successfully
        """
        self.manager = BagRecordingManager(self.experiment_config)

        # Start recording
        if not self.manager.start_record(iteration, output_path, base_name):
            return False

        # Wait for bag directory creation
        bag_dir = os.path.join(output_path, f"{base_name}_{iteration:02d}")
        max_wait = 10
        wait_time = 0
        while not os.path.exists(bag_dir) and wait_time < max_wait:
            time.sleep(0.5)
            wait_time += 0.5

        if not os.path.exists(bag_dir):
            logger.error(f"Bag directory was not created at {bag_dir}")
            return False

        # Start Docker containers
        logger.info("Starting Docker containers...")
        self.manager.process_manager.run_command(
            "make run",
            cwd=self.manager.granite_ros_path,
            output_dir=bag_dir,
            log_prefix="granite_ros",
        )
        self.manager.process_manager.run_command(
            "make run",
            cwd=self.manager.spl_gnss_denied_path,
            output_dir=bag_dir,
            log_prefix="gnss_sync",
        )

        time.sleep(100.0)

        # Play the bag file
        logger.info("Playing bag file...")
        play_process = subprocess.Popen(
            f"ros2 bag play {self.bag_path}",
            shell=True,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
        )

        return self._monitor_experiment(play_process)

    def _monitor_experiment(self, play_process: subprocess.Popen) -> bool:
        """
        Monitor the experiment execution with progress bar.

        Args:
            play_process: Subprocess running the bag playback

        Returns:
            bool: True if experiment completed successfully
        """
        # Get expected total messages from the bag file
        expected_messages = self.manager.get_topic_count(self.bag_path)
        if expected_messages == 0:
            logger.error("Failed to get message count from bag file")
            return False

        start_time = time.time()
        gnss_disabled = False
        last_count = self.counter_node.count
        stuck_time = time.time()

        # Initialize progress bar
        pbar = tqdm(
            total=expected_messages,
            desc="Processing messages",
            unit="msgs",
            dynamic_ncols=True,
        )
        last_update = 0

        while True:
            current_time = time.time() - start_time
            if self.gnss_cutoff_time >= 0 and current_time >= self.gnss_cutoff_time and not gnss_disabled:
                logger.info(f"Disabling GNSS input at {current_time:.2f} seconds...")
                self._disable_gnss()
                gnss_disabled = True

            # Update progress bar
            messages_diff = self.counter_node.count - last_update
            if messages_diff > 0:
                pbar.update(messages_diff)
                last_update = self.counter_node.count

            if self.counter_node.count == last_count:
                if time.time() - stuck_time > 10.0:
                    logger.info("No new messages received for 10 seconds, stopping...")
                    break
            else:
                last_count = self.counter_node.count
                stuck_time = time.time()

            time.sleep(0.02)

        pbar.close()
        return self._cleanup_processes(play_process)

    def _disable_gnss(self):
        """Disable GNSS input through ROS2 service call."""
        try:
            subprocess.run(
                "ros2 service call /gnss_sync_node/disable_gnss_input std_srvs/srv/Empty {}",
                shell=True,
                timeout=5.0,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
            )
        except subprocess.TimeoutExpired:
            logger.error("GNSS disable service call timed out")

    def _cleanup_processes(self, play_process: subprocess.Popen) -> bool:
        """
        Clean up all processes after experiment completion.

        Args:
            play_process: Subprocess running the bag playback

        Returns:
            bool: True if cleanup was successful
        """
        try:
            logger.info("Terminating bag play process...")
            play_process.terminate()
            play_process.wait(timeout=5)
        except Exception as e:
            logger.error(f"Error stopping bag play process: {e}")
            play_process.kill()

        time.sleep(2)

        try:
            # Kill all ROS2-related processes
            cleanup_commands = [
                "pkill -f 'ros2 bag play'",
                "pkill -f 'ros2 service call'",
                "pkill -f 'ros2 bag record'",
            ]

            for cmd in cleanup_commands:
                logger.info(f"Running cleanup command: {cmd}")
                subprocess.run(
                    cmd,
                    shell=True,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                )

        except Exception as e:
            logger.error(f"Error during process cleanup: {e}")

        logger.info("Stopping recording...")
        self.manager.stop_record()
        logger.info("Recording stopped")

        return True

    def cleanup(self):
        """Clean up all resources."""
        if self.manager:
            self.manager.cleanup()
        try:
            # Kill any remaining ROS2 processes
            cleanup_commands = [
                "pkill -f 'ros2 bag play'",
                "pkill -f 'ros2 service call'",
                "pkill -f 'ros2 bag record'",
            ]

            for cmd in cleanup_commands:
                logger.info(f"Running final cleanup command: {cmd}")
                subprocess.run(
                    cmd,
                    shell=True,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                )

            rclpy.shutdown()
            if self.ros_thread:
                self.ros_thread.join()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


class AimLogger:
    """
    Manages experiment tracking with Aim.

    Handles logging of experiment metrics, parameters, and visualizations
    to Aim for experiment tracking and analysis. Supports logging of
    both scalar metrics and Plotly figures.
    """

    def __init__(self, experiment_name: str, config: dict):
        """
        Initialize Aim run with experiment configuration.

        Args:
            experiment_name: Name of the experiment
            config: Experiment configuration dictionary

        Note:
            Creates a new Aim run and logs initial configuration
        """
        self.run = Run(experiment=experiment_name)
        # Log experiment configuration
        self.run["config"] = config
        self.run["params"] = config["optimization_params"]
        self.base_name = get_bag_name_from_path(config["input_bag_path"])

    def log_metrics(self, param_name: str, param_value: float, iteration: int, metrics_path: str):
        """
        Log evaluation metrics from RPE and ATE files.

        Args:
            param_name: Name of the parameter being optimized
            param_value: Value of the parameter
            iteration: Iteration number
            metrics_path: Path to the evaluation metrics directory
        """
        context = {
            "bag_file": self.base_name,
            "parameter": param_name,
            "iteration": iteration,
        }

        # Process RPE metrics
        rpe_file = os.path.join(metrics_path, "rpe_result.txt")
        if os.path.exists(rpe_file):
            try:
                with open(rpe_file, "r") as f:
                    rpe_content = f.read()

                # Extract metrics from RPE content using regular expressions
                metric_patterns = {
                    "max": r"max\s+([\d.]+)",
                    "mean": r"mean\s+([\d.]+)",
                    "median": r"median\s+([\d.]+)",
                    "min": r"min\s+([\d.]+)",
                    "rmse": r"rmse\s+([\d.]+)",
                    "sse": r"sse\s+([\d.]+)",
                    "std": r"std\s+([\d.]+)",
                }

                for metric_name, pattern in metric_patterns.items():
                    match = re.search(pattern, rpe_content)
                    if match:
                        value = float(match.group(1))
                        self.run.track(
                            value,
                            name=f"rpe_{metric_name}",
                            context=context,
                            step=param_value,
                        )
                        logger.info(f"RPE {metric_name}: {value:.6f}")
                    else:
                        logger.warning(f"Could not find RPE {metric_name} in file")

            except Exception as e:
                logger.error(f"Error processing RPE file: {str(e)}")

        # Process ATE metrics
        ate_file = os.path.join(metrics_path, "ate_result.txt")
        if os.path.exists(ate_file):
            try:
                with open(ate_file, "r") as f:
                    ate_content = f.read()

                # Extract metrics from ATE content
                metric_patterns = {
                    "max": r"max\s+([\d.]+)",
                    "mean": r"mean\s+([\d.]+)",
                    "median": r"median\s+([\d.]+)",
                    "min": r"min\s+([\d.]+)",
                    "rmse": r"rmse\s+([\d.]+)",
                    "sse": r"sse\s+([\d.]+)",
                    "std": r"std\s+([\d.]+)",
                }

                for metric_name, pattern in metric_patterns.items():
                    match = re.search(pattern, ate_content)
                    if match:
                        value = float(match.group(1))
                        self.run.track(
                            value,
                            name=f"ate_{metric_name}",
                            context=context,
                            step=param_value,
                        )
                        logger.info(f"ATE {metric_name}: {value:.6f}")
                    else:
                        logger.warning(f"Could not find ATE {metric_name} in file")

            except Exception as e:
                logger.error(f"Error processing ATE file: {e}")

    def log_plotly_figure(
        self,
        fig: go.Figure,
        name: str,
        param_name: str,
        param_value: float,
        iteration: int,
    ):
        """
        Log Plotly figure to Aim.

        Args:
            fig: Plotly figure object
            name: Name of the figure
            param_name: Name of the parameter being optimized
            param_value: Value of the parameter
            iteration: Iteration number
        """
        context = {
            "bag_file": self.base_name,
            "parameter": param_name,
            "iteration": iteration,
        }

        self.run.track(fig, name=name, context=context, step=param_value)


def load_config(config_path: str) -> dict:
    """
    Load experiment configuration from YAML file.

    Args:
        config_path: Path to the YAML configuration file

    Returns:
        dict: Configuration parameters

    Raises:
        yaml.YAMLError: If YAML parsing fails
        FileNotFoundError: If config file doesn't exist
    """
    try:
        with open(config_path, "r") as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        raise


def extract_plotly_data(html_content: str) -> dict:
    """
    Extract Plotly figure data from HTML content.

    Args:
        html_content: HTML content containing Plotly figure

    Returns:
        dict: Plotly figure data

    Raises:
        ValueError: If no Plotly data is found or parsing fails
    """
    try:
        # Parse HTML content
        soup = BeautifulSoup(html_content, "html.parser")

        # Find the script tag containing Plotly data
        script_tags = soup.find_all("script")
        for script in script_tags:
            if "Plotly.newPlot" in script.string:
                # Extract the JSON data
                data_start = script.string.find("Plotly.newPlot(") + len("Plotly.newPlot(")
                data_end = script.string.find(");", data_start)
                plot_data = script.string[data_start:data_end].strip()

                # Remove the first parameter (div id) and parse the remaining JSON
                plot_data = ",".join(plot_data.split(",")[1:])
                return json.loads(plot_data)

        raise ValueError("No Plotly data found in HTML content")
    except Exception as e:
        raise ValueError(f"Error extracting Plotly data: {str(e)}")


def run_evaluation(
    bag_folder: str,
    config: dict,
    aim_logger: AimLogger,
    param_name: str,
    param_value: float,
    iteration: int,
):
    """
    Run the evaluation process and log results to Aim.

    Args:
        bag_folder: Path to the bag folder to evaluate
        config: Experiment configuration dictionary
        aim_logger: AimLogger instance
        param_name: Name of the parameter being optimized
        param_value: Value of the parameter
        iteration: Iteration number

    Note:
        Executes evaluation script and logs metrics and visualizations to Aim
    """
    logger.info("Starting evaluation process...")
    eval_command = (
        f"python {config['evaluation_script_path']} "
        f"-b {bag_folder} "
        f"-c {config['customer_config']} "
        f"-out {bag_folder}/evaluation"
    )

    try:
        subprocess.run(
            eval_command,
            shell=True,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
        )
        logger.info("Evaluation completed successfully")

        # Log metrics to Aim
        eval_dir = os.path.join(bag_folder, "evaluation")
        aim_logger.log_metrics(param_name, param_value, iteration, eval_dir)

    except subprocess.CalledProcessError as e:
        logger.error("Evaluation failed with error:\n%s", e.stderr)


def get_bag_name_from_path(bag_path: str) -> str:
    """
    Extract base name from bag path and remove extension.

    Args:
        bag_path: Full path to the bag file

    Returns:
        str: Base name without extension
    """
    base_name = os.path.basename(bag_path)
    return os.path.splitext(base_name)[0]


def main():
    """
    Main execution function for the VIO experiment pipeline.

    Coordinates the entire experiment workflow including:
    - Loading configuration
    - Setting up output directories
    - Running parameter optimization iterations
    - Managing experiment execution
    - Handling cleanup and logging

    Note:
        Entry point for the VIO experiment pipeline
    """
    # Load configuration
    config_file = Path(__file__).parent / "experiment_config.yaml"
    experiment_config = load_config(str(config_file))

    # Setup output directory
    output_base_path = experiment_config["output_base_path"]
    if not FileSystemManager.setup_output_directory(output_base_path):
        logger.error("Aborting due to output directory setup failure")
        return

    # Copy config file to output directory
    config_backup_path = os.path.join(output_base_path, "experiment_config.yaml")
    shutil.copy2(config_file, config_backup_path)

    # Get VIO config path
    config_path = experiment_config["vio_config_path"]

    # Get base name for output files
    input_bag_path = experiment_config["input_bag_path"]
    bag_base_name = get_bag_name_from_path(input_bag_path)

    # Get optimization parameters first
    opt_params = experiment_config["optimization_params"]
    param_name = opt_params["param_name"]

    # Initialize Aim logger
    aim_logger = AimLogger(experiment_name=f"vio_optimization_{bag_base_name}", config=experiment_config)

    # Initialize experiment runner
    experiment_runner = ExperimentRunner(config_path, output_base_path, experiment_config)

    # Print current parameter value
    logger.info("Checking current parameter value...")
    ConfigManager.print_config_structure(config_path, param_name)

    # Verify parameter exists in VIO config
    if not ConfigManager.verify_param_exists(config_path, param_name):
        logger.error(f"Parameter {param_name} not found in VIO config")
        logger.error("Please check the parameter path in the config file")
        return

    # Handle different parameter configurations
    if all(k in opt_params for k in ("start", "end", "step")):
        param_values = list(range(opt_params["start"], opt_params["end"], opt_params["step"]))
    elif "values" in opt_params:
        param_values = opt_params["values"]
    else:
        logger.error("Invalid parameter configuration - must specify either range (start, end, step) or values list")
        return

    # Process parameter values
    for param_value in param_values:
        logger.info(f"\n{'='*50}")
        logger.info(f"Testing {param_name}: {param_value}")
        logger.info(f"{'='*50}\n")

        # Create directory for this parameter value
        param_base_name = param_name.split(".")[-1]  # Extract the last part of the parameter path
        output_path = os.path.join(output_base_path, f"{param_base_name}_{param_value}")
        if not FileSystemManager.ensure_directory(output_path):
            logger.error(f"Failed to create output directory for {param_name}={param_value}")
            continue

        # Update configuration file
        if not ConfigManager.update_vio_param(config_path, param_name, param_value):
            logger.error(f"Skipping {param_name}={param_value} due to config update failure")
            continue

        # Run iterations for this configuration
        for iteration in range(experiment_config["iterations_per_config"]):
            logger.info(f"\n{'-'*40}")
            logger.info(
                f"Starting iteration {iteration + 1}/{experiment_config['iterations_per_config']} "
                f"for {param_name}={param_value}"
            )
            logger.info(f"{'-'*40}\n")

            try:
                # Setup ROS counter
                if not experiment_runner.setup_ros_counter():
                    logger.error("Failed to setup ROS counter")
                    continue

                # Run the experiment iteration
                success = experiment_runner.run_single_iteration(param_value, iteration, output_path, bag_base_name)

                if success:
                    # Verify the recording
                    bag_manager = BagRecordingManager(experiment_config)
                    if not bag_manager.verify_bag_recording(output_path, iteration, bag_base_name):
                        logger.error(f"Recording verification failed for iteration {iteration}")
                        continue

                    # Run evaluation
                    bag_folder = os.path.join(output_path, f"{bag_base_name}_{iteration:02d}")
                    run_evaluation(
                        bag_folder,
                        experiment_config,
                        aim_logger,
                        param_name,
                        param_value,
                        iteration,
                    )

                if iteration < experiment_config["iterations_per_config"] - 1:
                    logger.info("Waiting 10 seconds before next iteration...")
                    time.sleep(10)

            except KeyboardInterrupt:
                logger.info("\nReceived interrupt, cleaning up...")
                break
            except Exception as e:
                logger.error(f"An error occurred: {e}")
            finally:
                experiment_runner.cleanup()

            if "KeyboardInterrupt" in locals():
                break

    logger.info("\nAll configurations completed!")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("\nExperiment interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
    finally:
        logger.info("Experiment pipeline completed")
