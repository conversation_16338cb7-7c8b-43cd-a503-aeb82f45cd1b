import argparse
import csv
import logging.config
import os

from rosbags.rosbag2 import Reader
from rosbags.typesys import Stores, get_typestore

logging.config.fileConfig("../logging.conf")
logger = logging.getLogger(__name__)


def parser():
    parser = argparse.ArgumentParser(description="Extract GPS topic from a ros2 bag file and store data in CSV format")
    parser.add_argument("-n", "--topic_names", nargs="+", default=[], help="GPS topic names to extract")
    parser.add_argument("-i", "--input_bag_file", default="", help="Path of ros2 bag file")
    parser.add_argument("-o", "--output_file", default="./gps_data.csv", help="Output CSV file path")
    parser.add_argument("--start_offset", type=int, default=0, help="Number of GPS messages to skip from start")
    parser.add_argument(
        "--time_format",
        default="ros",
        choices=["ros", "unix"],
        help="Timestamp format: 'ros' for ROS timestamp or 'unix' for Unix timestamp",
    )

    return parser.parse_args()


def extract_gps_data(args):
    """Extract GPS data from a ros2 bag file and save to CSV."""
    typestore = get_typestore(Stores.ROS2_HUMBLE)
    gps_data = []
    skip_count = 0

    with Reader(args.input_bag_file) as reader:
        for connection, timestamp, rawdata in reader.messages():
            if connection.topic in args.topic_names:
                # Skip messages based on start_offset
                if skip_count < args.start_offset:
                    skip_count += 1
                    continue

                msg = typestore.deserialize_cdr(rawdata, connection.msgtype)

                # Handle different GPS message types
                if connection.msgtype == "sensor_msgs/msg/NavSatFix":
                    # Extract timestamp
                    if args.time_format == "ros":
                        timestamp_val = f"{msg.header.stamp.sec}.{msg.header.stamp.nanosec:09d}"
                    else:  # unix
                        timestamp_val = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9

                    # Extract GPS coordinates
                    latitude = msg.latitude
                    longitude = msg.longitude
                    altitude = msg.altitude

                    # Check if GPS fix is valid (not NaN)
                    if not (latitude != latitude or longitude != longitude or altitude != altitude):  # NaN check
                        gps_data.append([timestamp_val, latitude, longitude, altitude])
                        print(f"GPS data: timestamp={timestamp_val}, lat={latitude}, lon={longitude}, alt={altitude}")

                elif connection.msgtype == "geographic_msgs/msg/GeoPointStamped":
                    # Handle GeoPointStamped messages
                    if args.time_format == "ros":
                        timestamp_val = f"{msg.header.stamp.sec}.{msg.header.stamp.nanosec:09d}"
                    else:  # unix
                        timestamp_val = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9

                    latitude = msg.position.latitude
                    longitude = msg.position.longitude
                    altitude = msg.position.altitude

                    if not (latitude != latitude or longitude != longitude or altitude != altitude):  # NaN check
                        gps_data.append([timestamp_val, latitude, longitude, altitude])
                        print(f"GPS data: timestamp={timestamp_val}, lat={latitude}, lon={longitude}, alt={altitude}")

                else:
                    logger.warning(f"Unsupported GPS message type: {connection.msgtype}")
                    continue

    return gps_data


def save_to_csv(gps_data, output_file):
    """Save GPS data to CSV file."""
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    with open(output_file, "w", newline="") as csvfile:
        writer = csv.writer(csvfile)
        # Write header
        writer.writerow(["timestamp", "latitude", "longitude", "altitude"])
        # Write data
        writer.writerows(gps_data)

    print(f"GPS data saved to {output_file} with {len(gps_data)} records")


def main():
    args = parser()

    if not args.topic_names:
        logger.error("Please specify at least one GPS topic name using -n argument")
        return

    if not os.path.exists(args.input_bag_file):
        logger.error(f"Input bag file does not exist: {args.input_bag_file}")
        return

    logger.info(f"Extracting GPS data from topics: {args.topic_names}")
    logger.info(f"Input bag file: {args.input_bag_file}")
    logger.info(f"Output CSV file: {args.output_file}")

    # Extract GPS data
    gps_data = extract_gps_data(args)

    if gps_data:
        # Save to CSV
        save_to_csv(gps_data, args.output_file)
        logger.info(f"Successfully extracted {len(gps_data)} GPS records")
    else:
        logger.warning("No GPS data found in the specified topics")


if __name__ == "__main__":
    main()
