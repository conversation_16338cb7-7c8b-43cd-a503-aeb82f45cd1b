#!/usr/bin/env python3

import argparse
import os
import time
from typing import List, Tuple

import cv2
import numpy as np
import plotly.graph_objs as go
import plotly.io as pio
import plotly.subplots as make_subplots
from cv_bridge import CvBridge
from plotly.offline import plot
from rosbags.rosbag2 import Reader
from rosbags.typesys import Stores, get_typestore


class VIOVerificationProcessor:
    """
    Standalone VIO verification processor that reads ROS2 bag files directly
    and generates timestamp timing analysis plots for image and IMU data.

    This version processes ALL messages from the bag file without the limitations
    of ROS2 subscriptions (queue depth, QoS policies, etc.).
    """

    def __init__(self, bag_path: str, image_topic: str, imu_topic: str):
        self.bag_path = bag_path
        self.image_topic = image_topic
        self.imu_topic = imu_topic
        self.bridge = CvBridge()
        self.typestore = get_typestore(Stores.ROS2_HUMBLE)

        # Data storage
        self.flow_times = []
        self.flow_values = []
        self.image_timestamps = []

        self.imu_times = []
        self.imu_linear_acc_x = []
        self.imu_linear_acc_y = []
        self.imu_linear_acc_z = []
        self.imu_linear_acc_mag = []
        self.imu_angular_vel_x = []
        self.imu_angular_vel_y = []
        self.imu_angular_vel_z = []
        self.imu_angular_vel_mag = []

        # For optical flow computation
        self.prev_gray = None

    def process_bag(self) -> bool:
        """
        Process the entire bag file and extract all image and IMU messages.

        Returns:
            bool: True if processing was successful, False otherwise
        """
        if not os.path.exists(self.bag_path):
            print(f"Error: Bag path {self.bag_path} does not exist.")
            return False

        print(f"Processing bag: {self.bag_path}")
        print(f"Image topic: {self.image_topic}")
        print(f"IMU topic: {self.imu_topic}")

        image_count = 0
        imu_count = 0

        try:
            with Reader(self.bag_path) as reader:
                # Check available topics
                available_topics = {
                    conn.topic: conn.msgtype for conn in reader.connections
                }

                if self.image_topic not in available_topics:
                    print(f"Warning: Image topic '{self.image_topic}' not found in bag")
                    image_topics = [
                        t for t in available_topics.keys() if "image" in t.lower()
                    ]
                    if image_topics:
                        print(f"Available image topics: {image_topics}")

                if self.imu_topic not in available_topics:
                    print(f"Warning: IMU topic '{self.imu_topic}' not found in bag")
                    imu_topics = [
                        t for t in available_topics.keys() if "imu" in t.lower()
                    ]
                    if imu_topics:
                        print(f"Available IMU topics: {imu_topics}")

                # Process all messages
                for connection, timestamp, rawdata in reader.messages():
                    if connection.topic == self.image_topic:
                        try:
                            msg = self.typestore.deserialize_cdr(
                                rawdata, connection.msgtype
                            )
                            self._process_image_message(msg, connection.msgtype)
                            image_count += 1
                            if image_count % 1000 == 0:
                                print(f"Processed {image_count} images...")
                        except Exception as e:
                            print(f"Error processing image message: {e}")

                    elif connection.topic == self.imu_topic:
                        try:
                            msg = self.typestore.deserialize_cdr(
                                rawdata, connection.msgtype
                            )
                            self._process_imu_message(msg)
                            imu_count += 1
                            if imu_count % 5000 == 0:
                                print(f"Processed {imu_count} IMU messages...")
                        except Exception as e:
                            print(f"Error processing IMU message: {e}")

        except Exception as e:
            print(f"Error reading bag file: {e}")
            return False

        print(f"Finished processing bag:")
        print(f"  Total images processed: {image_count}")
        print(f"  Total IMU messages processed: {imu_count}")
        print(f"  Optical flow calculations: {len(self.flow_times)}")

        return True

    def _process_image_message(self, msg, msg_type: str):
        """Process an image message and compute optical flow if possible."""
        timestamp_sec = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9
        self.image_timestamps.append(timestamp_sec)

        try:
            # Convert message to OpenCV image
            if msg_type == "sensor_msgs/msg/Image":
                current_img = self.bridge.imgmsg_to_cv2(msg)
            elif msg_type == "sensor_msgs/msg/CompressedImage":
                current_img = self.bridge.compressed_imgmsg_to_cv2(msg)
            else:
                print(f"Unsupported image message type: {msg_type}")
                return

            # Convert to grayscale if needed
            if len(current_img.shape) > 2:
                current_img = cv2.cvtColor(current_img, cv2.COLOR_BGR2GRAY)

            # Compute optical flow if we have a previous frame
            if self.prev_gray is not None:
                try:
                    flow = cv2.calcOpticalFlowFarneback(
                        self.prev_gray,
                        current_img,
                        None,
                        0.5,  # pyramid scale
                        3,  # levels
                        15,  # winsize
                        3,  # iterations
                        5,  # poly_n
                        1.2,  # poly_sigma
                        0,  # flags
                    )

                    # Compute average flow magnitude
                    flow_magnitude = np.sqrt(flow[..., 0] ** 2 + flow[..., 1] ** 2)
                    avg_flow_magnitude = np.mean(flow_magnitude)

                    self.flow_times.append(timestamp_sec)
                    self.flow_values.append(avg_flow_magnitude)
                except Exception as e:
                    # Skip optical flow computation if there are library issues
                    # This commonly happens with OpenCV/GDAL library compatibility
                    if len(self.flow_times) == 0:  # Only print once
                        print(
                            f"Warning: Optical flow computation disabled due to library issues"
                        )
                        print("Continuing with timestamp analysis only...")

            # Update previous frame
            self.prev_gray = current_img

        except Exception as e:
            print(f"Error processing image: {e}")

    def _process_imu_message(self, msg):
        """Process an IMU message."""
        timestamp_sec = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9

        # Extract linear acceleration components
        ax = msg.linear_acceleration.x
        ay = msg.linear_acceleration.y
        az = msg.linear_acceleration.z
        linear_acc_mag = np.sqrt(ax * ax + ay * ay + az * az)

        # Extract angular velocity components
        wx = msg.angular_velocity.x
        wy = msg.angular_velocity.y
        wz = msg.angular_velocity.z
        angular_vel_mag = np.sqrt(wx * wx + wy * wy + wz * wz)

        # Store data
        self.imu_times.append(timestamp_sec)
        self.imu_linear_acc_x.append(ax)
        self.imu_linear_acc_y.append(ay)
        self.imu_linear_acc_z.append(az)
        self.imu_linear_acc_mag.append(linear_acc_mag)
        self.imu_angular_vel_x.append(wx)
        self.imu_angular_vel_y.append(wy)
        self.imu_angular_vel_z.append(wz)
        self.imu_angular_vel_mag.append(angular_vel_mag)

    def generate_plots(self):
        """Generate all plots using the processed data."""
        self.plot_results()
        self.plot_imu_components()
        self.plot_timestamp_timing_analysis()

    def plot_results(self):
        """
        Plots the stored optical flow magnitude and IMU linear acceleration
        magnitude vs. time.
        """
        if not self.flow_times or not self.imu_times:
            print("No data to plot for flow vs IMU.")
            return

        flow_times = np.array(self.flow_times)
        flow_values = np.array(self.flow_values)
        imu_times = np.array(self.imu_times)
        imu_linear_acc_mag = np.array(self.imu_linear_acc_mag)

        if len(flow_times) > 0:
            fidx = np.argsort(flow_times)
            flow_times = flow_times[fidx]
            flow_values = flow_values[fidx]

        if len(imu_times) > 0:
            iidx = np.argsort(imu_times)
            imu_times = imu_times[iidx]
            imu_linear_acc_mag = imu_linear_acc_mag[iidx]

        fig = go.Figure()

        if len(flow_times) > 0:
            print(f"First image timestamp: {flow_times[0]}")
            print(f"Last image timestamp: {flow_times[-1]}")
            fig.add_trace(
                go.Scatter(
                    x=flow_times.astype("datetime64[s]"),
                    y=flow_values,
                    mode="lines+markers",
                    name="Optical Flow Magnitude",
                    hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>Value: %{y:.6f}<extra></extra>",
                )
            )

        if len(imu_times) > 0:
            print(f"First IMU timestamp: {imu_times[0]}")
            print(f"Last IMU timestamp: {imu_times[-1]}")
            fig.add_trace(
                go.Scatter(
                    x=imu_times.astype("datetime64[s]"),
                    y=imu_linear_acc_mag,
                    mode="lines+markers",
                    name="IMU Linear Acceleration Magnitude",
                    hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>Value: %{y:.6f}<extra></extra>",
                )
            )

        fig.update_layout(
            title={
                "text": "Optical Flow vs. IMU Linear Acceleration Magnitude",
                "x": 0.5,
                "yanchor": "top",
            },
            xaxis=dict(
                title="Time (s)",
            ),
            yaxis_title="Magnitude",
        )

        plot(fig, filename="flow_imu_plot.html", auto_open=False)
        pio.write_image(fig, "flow_imu_plot.png")

    def plot_imu_components(self):
        """
        Creates a second plot with two subplots:
        1. Linear acceleration (x, y, z components and magnitude)
        2. Angular velocity (x, y, z components and magnitude)
        """
        if not self.imu_times:
            print("No IMU data to plot components.")
            return

        imu_times = np.array(self.imu_times)

        # Sort to ensure time is in ascending order
        if len(imu_times) > 0:
            iidx = np.argsort(imu_times)
            imu_times = imu_times[iidx].astype("datetime64[s]")

            # Sort all IMU data
            self.imu_linear_acc_x = np.array(self.imu_linear_acc_x)[iidx]
            self.imu_linear_acc_y = np.array(self.imu_linear_acc_y)[iidx]
            self.imu_linear_acc_z = np.array(self.imu_linear_acc_z)[iidx]
            self.imu_linear_acc_mag = np.array(self.imu_linear_acc_mag)[iidx]

            self.imu_angular_vel_x = np.array(self.imu_angular_vel_x)[iidx]
            self.imu_angular_vel_y = np.array(self.imu_angular_vel_y)[iidx]
            self.imu_angular_vel_z = np.array(self.imu_angular_vel_z)[iidx]
            self.imu_angular_vel_mag = np.array(self.imu_angular_vel_mag)[iidx]

        fig = make_subplots.make_subplots(
            rows=2,
            cols=1,
            subplot_titles=(
                "IMU Linear Acceleration Components",
                "IMU Angular Velocity Components",
            ),
            shared_xaxes=True,
            vertical_spacing=0.1,
        )

        # Add linear acceleration traces
        fig.add_trace(
            go.Scatter(
                x=imu_times,
                y=self.imu_linear_acc_x,
                mode="lines+markers",
                name="Linear Acc. X",
                line=dict(color="red"),
                hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>X: %{y:.6f} m/s²<extra></extra>",
            ),
            row=1,
            col=1,
        )

        fig.add_trace(
            go.Scatter(
                x=imu_times,
                y=self.imu_linear_acc_y,
                mode="lines+markers",
                name="Linear Acc. Y",
                line=dict(color="green"),
                hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>Y: %{y:.6f} m/s²<extra></extra>",
            ),
            row=1,
            col=1,
        )

        fig.add_trace(
            go.Scatter(
                x=imu_times,
                y=self.imu_linear_acc_z,
                mode="lines+markers",
                name="Linear Acc. Z",
                line=dict(color="blue"),
                hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>Z: %{y:.6f} m/s²<extra></extra>",
            ),
            row=1,
            col=1,
        )

        fig.add_trace(
            go.Scatter(
                x=imu_times,
                y=self.imu_linear_acc_mag,
                mode="lines+markers",
                name="Linear Acc. Magnitude",
                line=dict(color="gray"),
                hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>Mag: %{y:.6f} m/s²<extra></extra>",
            ),
            row=1,
            col=1,
        )

        # Add angular velocity traces
        fig.add_trace(
            go.Scatter(
                x=imu_times,
                y=self.imu_angular_vel_x,
                mode="lines+markers",
                name="Angular Vel. X",
                line=dict(color="cyan"),
                hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>X: %{y:.6f} rad/s<extra></extra>",
            ),
            row=2,
            col=1,
        )

        fig.add_trace(
            go.Scatter(
                x=imu_times,
                y=self.imu_angular_vel_y,
                mode="lines+markers",
                name="Angular Vel. Y",
                line=dict(color="magenta"),
                hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>Y: %{y:.6f} rad/s<extra></extra>",
            ),
            row=2,
            col=1,
        )

        fig.add_trace(
            go.Scatter(
                x=imu_times,
                y=self.imu_angular_vel_z,
                mode="lines+markers",
                name="Angular Vel. Z",
                line=dict(color="yellow"),
                hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>Z: %{y:.6f} rad/s<extra></extra>",
            ),
            row=2,
            col=1,
        )

        fig.add_trace(
            go.Scatter(
                x=imu_times,
                y=self.imu_angular_vel_mag,
                mode="lines+markers",
                name="Angular Vel. Magnitude",
                line=dict(color="gray"),
                hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>Mag: %{y:.6f} rad/s<extra></extra>",
            ),
            row=2,
            col=1,
        )

        # Update layout
        fig.update_layout(
            title={
                "text": "IMU Components",
                "x": 0.5,
                "xanchor": "center",
            },
        )

        fig.update_xaxes(title_text="Time (s)", row=2, col=1)
        fig.update_yaxes(title_text="Linear Acceleration (m/s²)", row=1, col=1)
        fig.update_yaxes(title_text="Angular Velocity (rad/s)", row=2, col=1)

        plot(fig, filename="imu_components_plot.html", auto_open=False)
        pio.write_image(fig, "imu_components_plot.png")

    def plot_timestamp_timing_analysis(self):
        """
        Creates a plot with two subplots showing image and IMU timestamps
        to visualize potential frame drops.
        """
        if not self.image_timestamps and not self.imu_times:
            print("No timestamp data to plot for image/IMU timestamp visualization.")
            return

        fig = make_subplots.make_subplots(
            rows=2,
            cols=1,
            subplot_titles=("Image Timestamps", "IMU Timestamps"),
            shared_xaxes=True,  # Timestamps are on the same x-axis
            vertical_spacing=0.15,
        )

        print(f"Image timestamps: {len(self.image_timestamps)}")
        print(f"IMU timestamps: {len(self.imu_times)}")

        # Image Timestamps
        if self.image_timestamps:
            image_ts_np = np.array(self.image_timestamps)

            x_image_lines = []
            y_image_lines = []
            for ts in image_ts_np:
                x_image_lines.extend(
                    [ts.astype("datetime64[s]"), ts.astype("datetime64[s]"), None]
                )  # X coordinates for vertical line
                y_image_lines.extend([0, 1, None])  # Y coordinates (0 to 1)

            fig.add_trace(
                go.Scatter(
                    x=x_image_lines,
                    y=y_image_lines,
                    mode="lines",
                    name="Image Data",
                    line=dict(color="blue"),
                    hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<extra></extra>",
                ),
                row=1,
                col=1,
            )
            fig.update_yaxes(title_text="Existence", range=[0, 1.1], row=1, col=1)
        else:
            print("No image timestamp data to plot for timestamp visualization.")

        # IMU Timestamps
        if self.imu_times:
            imu_ts_np = np.array(self.imu_times)

            x_imu_lines = []
            y_imu_lines = []
            for ts in imu_ts_np:
                x_imu_lines.extend(
                    [ts.astype("datetime64[s]"), ts.astype("datetime64[s]"), None]
                )  # X coordinates for vertical line
                y_imu_lines.extend([0, 1, None])  # Y coordinates (0 to 1)

            fig.add_trace(
                go.Scatter(
                    x=x_imu_lines,
                    y=y_imu_lines,
                    mode="lines",
                    name="IMU Data",
                    line=dict(color="green"),
                    hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<extra></extra>",
                ),
                row=2,
                col=1,
            )
            fig.update_yaxes(title_text="Existence", range=[0, 1.1], row=2, col=1)
        else:
            print("No IMU timestamp data to plot for timestamp visualization.")

        fig.update_layout(
            title={
                "text": "Image and IMU Timestamp Visualization",
                "x": 0.5,
                "xanchor": "center",
            },
        )

        plot(fig, filename="timestamp_visualization_plot.html", auto_open=False)
        pio.write_image(fig, "timestamp_visualization_plot.png")
        print(
            "Timestamp visualization plot saved as timestamp_visualization_plot.html/.png"
        )


def main():
    parser = argparse.ArgumentParser(
        description="Standalone VIO verification plots - processes ALL messages from ROS2 bag files"
    )
    parser.add_argument(
        "--bag-path", type=str, required=True, help="Path to ROS2 bag file"
    )
    parser.add_argument(
        "--image-topic", type=str, required=True, help="Image topic name"
    )
    parser.add_argument("--imu-topic", type=str, required=True, help="IMU topic name")

    args = parser.parse_args()

    print("=== VIO Verification Plots - Standalone Version ===")
    print("This version processes ALL messages from the bag file without loss")
    print()

    processor = VIOVerificationProcessor(
        bag_path=args.bag_path, image_topic=args.image_topic, imu_topic=args.imu_topic
    )

    if processor.process_bag():
        print("\nGenerating plots...")
        processor.generate_plots()
        print("All plots generated successfully!")
        print("\nGenerated files:")
        print("  - flow_imu_plot.html/.png")
        print("  - imu_components_plot.html/.png")
        print("  - timestamp_visualization_plot.html/.png")
    else:
        print("Failed to process bag file")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
