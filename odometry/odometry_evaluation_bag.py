import argparse
import logging.config
import os

from metric.pose_metric import APEMetric
from utils.bag_loader import BagLoader
from utils.lie_group_ops import sync_gt_with_estimated_odometry_msgs


def parser() -> argparse.Namespace:
    # create the top-level parser
    parsers = argparse.ArgumentParser(description="Odometry Evaluation from bag file")
    input_opts = parsers.add_argument_group("input")
    input_opts.add_argument("-b", "--bag_file", type=str, default="", help="Path bag file")
    input_opts.add_argument("-gt", "--gt_topic", type=str, default="", help="Odometry gt topic name")
    input_opts.add_argument("-n", "--topic_names", nargs="+", default=[])

    algo_opts = parsers.add_argument_group("algorithm options")
    algo_opts.add_argument(
        "-r",
        "--pose_relation",
        default="trans_part",
        help="pose relation on which the RPE is based",
        choices=[
            "full",
            "trans_part",
            "rot_part",
            "angle_deg",
            "angle_rad",
            "point_distance",
            "point_distance_error_ratio",
        ],
    )
    algo_opts.add_argument("-a", "--align", help="alignment with Umeyama's method", action="store_true")
    algo_opts.add_argument("-s", "--correct_scale", action="store_true", help="correct scale with Umeyama's method")
    algo_opts.add_argument(
        "--n_to_align",
        help="the number of poses to use for Umeyama alignment, " "counted from the start (default: all)",
        default=-1,
        type=int,
    )
    algo_opts.add_argument(
        "--align_origin",
        help="align the trajectory origin to the origin of the reference " "trajectory",
        action="store_true",
    )

    output_opts = parsers.add_argument_group("output options")
    output_opts.add_argument("-no_2d", "--disable_plot_2d", help="disable draw 2D plot", action="store_true")
    output_opts.add_argument("-no_3d", "--disable_plot_3d", help="disable draw 3D plot", action="store_true")
    output_opts.add_argument("-out", "--output_folder", default="", help="path to save result")

    args, _ = parsers.parse_known_args()
    return args


def load_logger():
    # Get the current file's path
    current_folder = os.path.dirname(os.path.abspath(__file__))

    # Construct the file location relative to the current folder or main Python module folder path
    file_location = os.path.join(current_folder, "logging.conf")

    # Use the modified file location in the fileConfig function
    logging.config.fileConfig(file_location)


def main():
    # Load logger
    load_logger()
    logger = logging.getLogger("odom_eval")
    args = parser()

    if not os.path.exists(args.output_folder):
        os.makedirs(args.output_folder)

    # Load the odometry topics from bag file
    bl = BagLoader()
    logger.info("Reading data from bag file ...")
    topic_msgs, topic_timestamps = bl.read_topics_bag(args.bag_file, args.topic_names)

    # Synchronize ground truth (gt) and estimated odometry data. For mis-sized trajectories, interpolate the pose.
    gt_traj, est_traj = sync_gt_with_estimated_odometry_msgs(topic_msgs[args.gt_topic], topic_msgs[args.topic_names[1]])
    logger.info("GT information %s", gt_traj)
    logger.info("ES information %s", est_traj)

    bag_filename = os.path.basename(args.bag_file)

    # Compute the APE metric
    ape_metric = APEMetric(args, gt_traj, est_traj)
    ape_result = ape_metric.compute()

    # Draw 2D and 3D plots
    if not args.disable_plot_2d:
        ape_metric.plot_2d(
            topic_msgs=topic_msgs,
            bag_filename=bag_filename,
            output_folder=args.output_folder,
        )

    if not args.disable_plot_3d:
        ape_metric.plot_3d(bag_filename)

    result_text_filename = os.path.join(args.output_folder, "ape_result.txt")
    with open(result_text_filename, "w", encoding="utf-8") as f:
        f.write(str(ape_result))


if __name__ == "__main__":
    main()
