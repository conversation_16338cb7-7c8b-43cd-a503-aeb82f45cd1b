import argparse
import logging.config
import os

from metric.pose_metric import APEMetric, KittiMetric, RPEMetric, TranslationalDriftPerSecondMetric
from utils.bag_loader import BagLoader
from utils.lie_group_ops import create_trajectory_from_relative_pose
from utils.point3d_ops import sync_gt_with_estimated_gps_msgs, sync_gt_with_estimated_vo_msgs
from utils.visualization import (
    geo_map,
    geo_map_error,
    plot_frequency,
    plot_kitti_translation_error,
    plot_translation_drift_per_seconds,
)


def parser():
    parsers = argparse.ArgumentParser(description="GPS Evaluation from bag file")
    input_opts = parsers.add_argument_group("input")
    input_opts.add_argument("-b", "--bag_file", type=str, default="", help="Path bag file")
    input_opts.add_argument("-gt", "--gt_topic", type=str, default="", help="GPS gt topic name")
    input_opts.add_argument(
        "-n",
        "--topic_names",
        nargs="+",
        default=[],
        help="List of required topic names",
    )
    input_opts.add_argument("-c", "--config_file", type=str, default="", help="Config file with topic names")

    algo_opts = parsers.add_argument_group("algorithm options")
    algo_opts.add_argument(
        "-r",
        "--pose_relation",
        default="trans_part",
        help="pose relation on which the RPE is based",
        choices=[
            "full",
            "trans_part",
            "rot_part",
            "angle_deg",
            "angle_rad",
            "point_distance",
            "point_distance_error_ratio",
        ],
    )
    algo_opts.add_argument("-a", "--align", help="alignment with Umeyama's method", action="store_true")
    algo_opts.add_argument(
        "-s",
        "--correct_scale",
        action="store_true",
        help="correct scale with Umeyama's method",
    )
    algo_opts.add_argument(
        "--n_to_align",
        help="the number of poses to use for Umeyama alignment, counted from the start (default: all)",
        default=-1,
        type=int,
    )
    algo_opts.add_argument(
        "--align_origin",
        help="align the trajectory origin to the origin of the reference trajectory",
        action="store_true",
    )

    algo_opts.add_argument(
        "-d",
        "--delta",
        type=float,
        default=1,
        help="""delta parameter represents the distance interval used to match poses for relative pose estimation."
        "It defines the spatial gap between consecutive poses that should be considered when calculating the relative"
        "transformation or movement from one pose to another""",
    )
    algo_opts.add_argument(
        "-t",
        "--delta_tol",
        type=float,
        default=0.1,
        help="relative delta tolerance for all_pairs mode",
    )
    algo_opts.add_argument(
        "-u",
        "--delta_unit",
        default="f",
        help="unit of delta - `f` (frames), `d` (deg), `r` (rad), `m`(meters)",
        choices=["f", "d", "r", "m"],
    )

    output_opts = parsers.add_argument_group("output options")
    output_opts.add_argument(
        "-no_map",
        "--disable_plot_geo_map",
        help="disable geographic map plot",
        action="store_true",
    )
    output_opts.add_argument(
        "-no_freq",
        "--disable_plot_frequency",
        help="disable frequency plot",
        action="store_true",
    )
    output_opts.add_argument("-no_2d", "--disable_plot_2d", help="disable draw 2D plot", action="store_true")
    output_opts.add_argument("-no_3d", "--disable_plot_3d", help="disable draw 3D plot", action="store_true")
    output_opts.add_argument("-out", "--output_folder", default="", help="path to save result")
    output_opts.add_argument(
        "-show",
        "--show_plots",
        help="show plots in addition to saving them",
        action="store_true",
    )

    args, _ = parsers.parse_known_args()
    return args


def load_logger():
    # Get the current file's path
    current_folder = os.path.dirname(os.path.abspath(__file__))

    # Construct the file location relative to the current folder or main Python module folder path
    file_location = os.path.join(current_folder, "logging.conf")

    # Use the modified file location in the fileConfig function
    logging.config.fileConfig(file_location)


def main():
    # Load logger
    load_logger()
    logger = logging.getLogger("odom_eval")
    args = parser()

    if not os.path.exists(args.output_folder):
        os.makedirs(args.output_folder)

    # Load the config file
    bl = BagLoader()
    logger.info("Reading topic names from config file ...")
    topic_dict, topic_names = bl.read_config_file(args.config_file)

    # Load the gps topics from bag file
    logger.info("Reading data from bag file ...")
    topic_msgs, topic_timestamps = bl.read_topics_bag(args.bag_file, topic_names)

    # Check the size of gt_topic and sync_topic
    gt_topic_msgs = topic_msgs[topic_dict["gt_topic"]]
    sync_topic_msgs = topic_msgs[topic_dict["sync_topic"]]
    relative_topic_msgs = topic_msgs[topic_dict["relative_topic"]]
    bag_filename = os.path.basename(args.bag_file)

    bl.print_topic_statistics(bag_filename, topic_msgs)

    if gt_topic_msgs.size == 0:
        logger.error("GT messages are empty. No GPS data available.")
        return

    if sync_topic_msgs.size:
        # Sync the gt and estimated gps data. Convert them to cartesian
        (
            gt_traj_enu,
            est_traj_enu,
            gt_traj_wgs84,
            est_traj_wgs84,
        ) = sync_gt_with_estimated_gps_msgs(gt_wgs84_msg_np=gt_topic_msgs, est_wgs84_msg_np=sync_topic_msgs)

        logger.info("GT information %s", gt_traj_enu)
        logger.info("ES information %s", est_traj_enu)

        kitti_metric = KittiMetric(gt_traj_enu, est_traj_enu)
        _, kitti_tra = kitti_metric.compute()

        out_kitti_translation_error_filename = os.path.join(args.output_folder, "kitti_translation_error_ape")
        plot_kitti_translation_error(
            translation_errors=kitti_tra,
            lengths=kitti_metric.lengths,
            title="KITTI Translation Error (%)",
            filename=out_kitti_translation_error_filename,
            show_plot=args.show_plots,
        )

        # Compute the APE metric
        ape_metric = APEMetric(args, gt_traj_enu, est_traj_enu)
        ape_result = ape_metric.compute()

        # Draw geographical plots on open street map
        if not args.disable_plot_geo_map:
            out_geo_filename = os.path.join(args.output_folder, "geo_map")
            geo_map(
                topic_msgs=topic_msgs,
                topic_dict=topic_dict,
                bag_filename=bag_filename,
                filename=out_geo_filename,
                show_plot=args.show_plots,
            )

            out_geo_error_filename = os.path.join(args.output_folder, "geo_map_error")
            geo_map_error(
                topic_msgs=topic_msgs,
                topic_dict=topic_dict,
                error=ape_result.np_arrays["error_array"],
                bag_filename=bag_filename,
                filename=out_geo_error_filename,
                show_plot=args.show_plots,
            )

        # Draw 2D and 3D plots
        if not args.disable_plot_2d:
            ape_metric.plot_2d(
                topic_msgs=topic_msgs,
                topic_dict=topic_dict,
                output_folder=args.output_folder,
                bag_filename=bag_filename,
                gt_traj_wgs84=gt_traj_wgs84,
                est_traj_wgs84=est_traj_wgs84,
            )

        if not args.disable_plot_3d:
            ape_metric.plot_3d(
                topic_msgs=topic_msgs,
                topic_dict=topic_dict,
                bag_filename=bag_filename,
            )
            ape_metric.plot_3d_cov(
                topic_msgs=topic_msgs,
                topic_dict=topic_dict,
                bag_filename=bag_filename,
            )

        if not args.disable_plot_frequency:
            out_plot_frequency_filename = os.path.join(args.output_folder, "topic_frequency")
            plot_frequency(
                topic_timestamps=topic_timestamps,
                title=f"Frequency of Topics Over Time in {bag_filename}",
                filename=out_plot_frequency_filename,
                show_plot=args.show_plots,
            )

        result_text_filename = os.path.join(args.output_folder, "ape_result.txt")
        with open(result_text_filename, "w", encoding="utf-8") as f:
            f.write(str(ape_result))
    else:
        logger.warning("Synced GPS messages are empty. No GPS data available.")
        logger.warning("We cannot compute ATE metric.")

    if "relative_topic" in topic_dict and relative_topic_msgs.size:
        # Create absolute trajectory, from scaled relative poses
        est_abs_trajectory = create_trajectory_from_relative_pose(relative_topic_msgs)

        # Sync the gt and estimated visual odometry data. Convert them to cartesian
        gt_traj_enu, est_traj_enu = sync_gt_with_estimated_vo_msgs(
            gt_wgs84_msg_np=topic_msgs[topic_dict["gt_topic"]],
            est_enu_msg_np=est_abs_trajectory,
        )

        # Compute relative pose error
        rpe_metric = RPEMetric(args, gt_traj_enu, est_traj_enu)
        rpe_result = rpe_metric.compute(align=True, correct_scale=False, n_to_align=-1, align_origin=False)

        rpe_metric.plot_3d(
            topic_msgs=topic_msgs,
            topic_dict=topic_dict,
            bag_filename=bag_filename,
        )
        rpe_metric.plot_2d(
            topic_msgs=topic_msgs,
            topic_dict=topic_dict,
            bag_filename=bag_filename,
            output_folder=args.output_folder,
        )

        kitti_metric = KittiMetric(rpe_metric.traj_ref, rpe_metric.traj_est)
        kitti_rot, kitti_tra = kitti_metric.compute()

        out_kitti_translation_error_filename = os.path.join(args.output_folder, "kitti_translation_error_rpe")
        plot_kitti_translation_error(
            translation_errors=kitti_tra,
            lengths=kitti_metric.lengths,
            title="KITTI Translation Error (%)",
            filename=out_kitti_translation_error_filename,
            show_plot=args.show_plots,
        )

        trans_drift_metric = TranslationalDriftPerSecondMetric(rpe_metric.traj_ref, rpe_metric.traj_est)
        translation_drift_per_sec, translation_drift_intervals = trans_drift_metric.compute()

        out_translation_drift_filename = os.path.join(args.output_folder, "translation_drift_per_sec_rpe")
        plot_translation_drift_per_seconds(
            translation_errors=translation_drift_per_sec,
            timestamps=translation_drift_intervals,
            title="Translation Drift Per Seconds (m/s)",
            filename=out_translation_drift_filename,
            show_plot=args.show_plots,
        )

        result_text_filename = os.path.join(args.output_folder, "rpe_result.txt")
        with open(result_text_filename, "w", encoding="utf-8") as f:
            f.write(str(rpe_result))
    else:
        logger.warning("Relative topic messages are empty. No relative data available.")
        logger.warning("We cannot compute RPE metric.")


if __name__ == "__main__":
    main()
