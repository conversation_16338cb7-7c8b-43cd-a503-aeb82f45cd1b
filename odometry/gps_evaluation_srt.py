import argparse
import logging.config
import os

from evo.tools.settings import SETTINGS
from metric.pose_metric import APEMetric
from utils.data_loader import DataLoader
from utils.point3d_ops import densify_trajectory


def parser():
    # create the top-level parser
    parsers = argparse.ArgumentParser(description="Odometry Evaluation for GPS Ground Truth data")
    input_opts = parsers.add_argument_group("input")
    input_opts.add_argument("-gt", "--gt_file", type=str, default="", help="Ground-truth srt file")
    input_opts.add_argument("-est", "--est_file", type=str, default="", help="Estimated cvs or txt file")
    input_opts.add_argument("-ft", "--flight_time", type=float, default="0.0", help="flight time in seconds")

    algo_opts = parsers.add_argument_group("algorithm options")
    algo_opts.add_argument(
        "-r",
        "--pose_relation",
        default="trans_part",
        help="pose relation on which the RPE is based",
        choices=[
            "full",
            "trans_part",
            "rot_part",
            "angle_deg",
            "angle_rad",
            "point_distance",
            "point_distance_error_ratio",
        ],
    )
    algo_opts.add_argument("-a", "--align", help="alignment with Umeyama's method", action="store_true")
    algo_opts.add_argument(
        "-s",
        "--correct_scale",
        action="store_true",
        help="correct scale with Umeyama's method",
    )
    algo_opts.add_argument(
        "--n_to_align",
        help="the number of poses to use for Umeyama alignment, " "counted from the start (default: all)",
        default=-1,
        type=int,
    )
    algo_opts.add_argument(
        "--align_origin",
        help="align the trajectory origin to the origin of the reference " "trajectory",
        action="store_true",
    )
    algo_opts.add_argument("--plot_2d", help="draw 2D plot", action="store_true")
    algo_opts.add_argument("--plot_3d", help="draw 3D plot", action="store_true")

    output_opts = parsers.add_argument_group("output options")
    output_opts.add_argument(
        "-p",
        "--plot",
        action="store_true",
        help="show plot window",
    )
    output_opts.add_argument(
        "--plot_mode",
        default=SETTINGS.plot_mode_default,
        help="the axes for plot projection",
        choices=["xy", "xz", "yx", "yz", "zx", "zy", "xyz"],
    )
    output_opts.add_argument(
        "--plot_x_dimension",
        choices=["index", "seconds", "distances"],
        default="seconds",
        help="dimension that is used on the x-axis of the raw value plot"
        "(default: seconds, or index if no timestamps are present)",
    )
    output_opts.add_argument(
        "--plot_colormap_max",
        type=float,
        help="the upper bound used for the color map plot " "(default: maximum error value)",
    )
    output_opts.add_argument(
        "--plot_colormap_min",
        type=float,
        help="the lower bound used for the color map plot " "(default: minimum error value)",
    )
    output_opts.add_argument(
        "--plot_colormap_max_percentile",
        type=float,
        help="percentile of the error distribution to be used "
        "as the upper bound of the color map plot "
        "(in %%, overrides --plot_colormap_max)",
    )
    output_opts.add_argument(
        "--ros_map_yaml",
        help="yaml file of an ROS 2D map image (.pgm/.png)" " that will be drawn into the plot",
        default=None,
    )
    output_opts.add_argument("--save_plot", default="", help="path to save plot")
    output_opts.add_argument("--serialize_plot", default=None, help="path to serialize plot (experimental)")
    output_opts.add_argument("--save_results", help=".zip file path to store results")
    output_opts.add_argument("--logfile", help="Local logfile path.", default=None)
    output_opts.add_argument(
        "--no_warnings",
        action="store_true",
        help="no warnings requiring user confirmation",
    )

    output_opts.add_argument("--output_folder", default="", help="path to save result")

    args, _ = parsers.parse_known_args()
    return args


def main():
    logging.config.fileConfig("./logging.conf")
    logger = logging.getLogger("odom_eval")

    # override the matplotlib logger and set the level to warning
    matplotlib_logger = logging.getLogger("matplotlib")
    matplotlib_logger.setLevel(logging.WARNING)

    args = parser()
    print(args)

    if not os.path.exists(args.output_folder):
        os.makedirs(args.output_folder)

    dl = DataLoader()
    logger.info("Reading GT data from srt files ...")
    gt_data = dl.load_srt_data(args.gt_file, "utm")
    logger.info("GT information %s", gt_data)

    bag_filename = os.path.basename(args.bag_file)

    if args.flight_time > 0.0:
        gt_data.timestamps = dl.create_custom_tms(args.flight_time, gt_data.num_poses)

    logger.info("Reading ES data from txt files ...")
    es_data = dl.load_txt_data(args.est_file, "ES", identity_rotation=True)
    logger.info("ES information %s", es_data)

    if gt_data.num_poses > es_data.num_poses:
        es_data = densify_trajectory(es_data, gt_data.num_poses, identity_rotation=True)
        if args.flight_time > 0.0:
            es_data.timestamps = dl.create_custom_tms(args.flight_time, gt_data.num_poses)
        logger.info("ES trajectory densified to size %s", es_data)

    ape_metric = APEMetric(args, gt_data, es_data)

    if args.n_to_align > 0:
        ape_result = ape_metric.compute(align=False, correct_scale=True, n_to_align=-1, align_origin=False)
        ape_result = ape_metric.compute(align=True, correct_scale=False)
    else:
        ape_result = ape_metric.compute()

    if args.plot_2d:
        ape_metric.plot_2d(bag_filename=bag_filename)

    if args.plot_3d:
        ape_metric.plot_3d(bag_filename)

    output_result = os.path.join(args.output_folder, "ape_result.txt")
    with open(output_result, "w") as f:
        f.write(str(ape_result))


if __name__ == "__main__":
    main()
