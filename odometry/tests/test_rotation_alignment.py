"""
Test suite for rotation alignment functionality used in trajectory evaluation metrics.
"""

import os
import sys

sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

import numpy as np  # noqa: E402
import pytest  # noqa: E402
from evo.core.trajectory import PoseTrajectory3D  # noqa: E402
from metric.pose_metric import TranslationalDriftPerSecondMetric  # noqa: E402
from scipy.spatial.transform import Rotation  # noqa: E402


class TestRotationAlignment:
    """Test suite for compute_rotation_alignment_from_positions function."""

    @pytest.fixture(autouse=True)
    def setup_test_data(self):
        """Set up test data using the same data from the Jupyter notebook."""
        t = np.linspace(0, 2 * np.pi, 32)
        self.gt_trajectory = np.stack([t, np.sin(t), np.zeros_like(t)], axis=1)
        self.anchor_idx = 15
        self.gt_trajectory -= self.gt_trajectory[self.anchor_idx]

        self.gt_rotation = Rotation.from_euler("xyz", [45, 10, -20], degrees=True)

        self.estimated_trajectory = self.gt_trajectory.copy()
        base_drift_vector = np.array([1.0, -1.0, 0.0]).reshape(1, 3)
        drift = np.linspace(0, 0.3, self.anchor_idx + 1).reshape(-1, 1) * base_drift_vector
        self.estimated_trajectory[self.anchor_idx + 1 :] += drift
        self.estimated_trajectory = self.gt_rotation.apply(self.estimated_trajectory)

        # Create timestamps according to the specification:
        # - Points 0-14 (before anchor): spread over 5 seconds
        # - Points 15-31 (anchor and after): totally 1 seconds
        timestamps = np.zeros(32)
        timestamps[0:15] = np.linspace(0.0, 5.0, 15)
        timestamps[15:32] = np.linspace(5.05, 6.0, 17)
        self.timestamps = timestamps

        num_poses = 32
        identity_quats = np.tile([1.0, 0.0, 0.0, 0.0], (num_poses, 1))

        self.traj_ref = PoseTrajectory3D(
            positions_xyz=self.gt_trajectory, orientations_quat_wxyz=identity_quats, timestamps=self.timestamps
        )

        self.traj_est = PoseTrajectory3D(
            positions_xyz=self.estimated_trajectory, orientations_quat_wxyz=identity_quats, timestamps=self.timestamps
        )

    def test_compute_local_alignment_rotation_recovery(self):
        """Test that compute_local_alignment recovers the correct rotation with high accuracy."""
        metric = TranslationalDriftPerSecondMetric(self.traj_ref, self.traj_est)

        end_time = 5.0  # Should have enough data for 10-second window
        alignment_matrix = metric.compute_local_alignment(end_time)
        rotation_part = alignment_matrix[:3, :3]
        recovered_rotation = Rotation.from_matrix(rotation_part)
        expected_rotation = self.gt_rotation.inv()
        expected_matrix = expected_rotation.as_matrix()
        recovered_matrix = recovered_rotation.as_matrix()

        assert np.allclose(expected_matrix, recovered_matrix, atol=0.1), (
            f"Recovered rotation doesn't match expected. Expected:\n{expected_matrix}\nRecovered:\n{recovered_matrix}"
        )

    def test_compute_local_alignment_error_reduction(self):
        """Test that compute_local_alignment and apply_alignment_to_position reduce trajectory error."""
        metric = TranslationalDriftPerSecondMetric(self.traj_ref, self.traj_est)

        end_time = 5.0  # Should have enough data for 10-second window
        alignment_matrix = metric.compute_local_alignment(end_time)

        ref_subset = self.gt_trajectory[: self.anchor_idx + 1]
        est_subset = self.estimated_trajectory[: self.anchor_idx + 1]

        est_aligned = np.array([metric.apply_alignment_to_position(pos, alignment_matrix) for pos in est_subset])

        error_before = np.mean(np.linalg.norm(ref_subset - est_subset, axis=1))
        error_after = np.mean(np.linalg.norm(ref_subset - est_aligned, axis=1))

        # Error should be significantly reduced after alignment
        assert error_after < error_before, (
            f"Alignment should reduce error. Before: {error_before:.6f}, After: {error_after:.6f}"
        )

        assert error_after < 0.1, f"Aligned trajectory error should be small, got {error_after:.6f}"

        est_aligned_direct = (alignment_matrix[:3, :3] @ est_subset.T).T

        assert np.allclose(est_aligned, est_aligned_direct, atol=1e-10), (
            "apply_alignment_to_position should give same result as direct matrix multiplication"
        )

    def test_compute_interval_drift_motion_vectors(self):
        """Test that compute_interval_drift correctly computes motion vectors and drift."""
        metric = TranslationalDriftPerSecondMetric(self.traj_ref, self.traj_est)

        end_time = 5.0  # Should have enough data for 10-second window
        alignment_matrix = metric.compute_local_alignment(end_time)

        start_idx = 15
        end_idx = 31

        drift_magnitude = metric.compute_interval_drift(start_idx, end_idx, alignment_matrix)

        # Calculate expected drift: drift was added as np.linspace(0, 0.3, anchor_idx + 1) * [1.0, -1.0, 0.0]
        # Maximum drift at end is 0.3 * [1.0, -1.0, 0.0] = [0.3, -0.3, 0.0]
        base_drift_vector = np.array([1.0, -1.0, 0.0])
        drift_factors = np.linspace(0, 0.3, self.anchor_idx + 1)
        added_drift_at_end = drift_factors[-1] * base_drift_vector
        added_drift_magnitude = np.linalg.norm(added_drift_at_end)

        dt = self.timestamps[end_idx] - self.timestamps[start_idx]
        expected_drift_per_second = added_drift_magnitude / dt

        assert np.isclose(drift_magnitude, expected_drift_per_second, atol=1e-6), (
            f"Computed drift {drift_magnitude:.6f} should match with drift per second {expected_drift_per_second:.6f}"
        )


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
